<?php

declare(strict_types=1);

namespace Modules\Template\Models;

use Core\Model;
use PDO;

/**
 * Template Model
 *
 * Handles data operations for document template management
 *
 * @package Modules\Template\Models
 */
class TemplateModel extends Model
{
    /**
     * Get all templates with filters
     *
     * @param array $filters Filters (jenis, status_aktif)
     * @return array List of templates
     */
    public function getTemplates(array $filters = []): array
    {
        try {
            $sql = "SELECT td.*, u.username as created_by_username
                    FROM template_dokumen td
                    JOIN users u ON td.created_by = u.id
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['jenis'])) {
                $sql .= " AND td.jenis = ?";
                $params[] = $filters['jenis'];
            }
            
            if (isset($filters['status_aktif'])) {
                $sql .= " AND td.status_aktif = ?";
                $params[] = $filters['status_aktif'];
            }
            
            $sql .= " ORDER BY td.jenis, td.nama";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching templates", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get template by ID
     *
     * @param int $id Template ID
     * @return array|null Template data
     */
    public function getTemplateById(int $id): ?array
    {
        try {
            $sql = "SELECT td.*, u.username as created_by_username
                    FROM template_dokumen td
                    JOIN users u ON td.created_by = u.id
                    WHERE td.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching template", ['id' => $id, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Create new template
     *
     * @param array $data Template data
     * @return int|null The new template ID or null if failed
     */
    public function createTemplate(array $data): ?int
    {
        try {
            $sql = "INSERT INTO template_dokumen (
                        nama, jenis, deskripsi, file_template, variables, 
                        status_aktif, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['nama'],
                $data['jenis'],
                $data['deskripsi'] ?? null,
                $data['file_template'],
                json_encode($data['variables'] ?? []),
                $data['status_aktif'] ?? true,
                $data['created_by']
            ]);

            if ($result) {
                $id = (int)$this->db->lastInsertId();
                $this->getLogger()->info("Template created", ['id' => $id, 'nama' => $data['nama']]);
                return $id;
            }

            return null;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error creating template", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Update template
     *
     * @param int $id Template ID
     * @param array $data Updated data
     * @return bool Success status
     */
    public function updateTemplate(int $id, array $data): bool
    {
        try {
            $sql = "UPDATE template_dokumen SET 
                        nama = ?, jenis = ?, deskripsi = ?, file_template = ?, 
                        variables = ?, status_aktif = ?, updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['nama'],
                $data['jenis'],
                $data['deskripsi'] ?? null,
                $data['file_template'],
                json_encode($data['variables'] ?? []),
                $data['status_aktif'] ?? true,
                $id
            ]);

            if ($result) {
                $this->getLogger()->info("Template updated", ['id' => $id]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error updating template", ['id' => $id, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Generate document from template
     *
     * @param int $templateId Template ID
     * @param array $variables Data variables
     * @param int $generatedBy User ID who generates the document
     * @param int|null $siswaId Optional student ID
     * @param int|null $kelasId Optional class ID
     * @return int|null The generated document ID or null if failed
     */
    public function generateDocument(int $templateId, array $variables, int $generatedBy, ?int $siswaId = null, ?int $kelasId = null): ?int
    {
        try {
            $this->db->beginTransaction();
            
            // Get template
            $template = $this->getTemplateById($templateId);
            if (!$template) {
                throw new \Exception("Template not found");
            }
            
            // Generate file name
            $fileName = $this->generateFileName($template, $variables);
            $filePath = $this->generateFilePath($fileName);
            
            // Process template with variables
            $content = $this->processTemplate($template['file_template'], $variables);
            
            // Save generated file
            if (!$this->saveGeneratedFile($filePath, $content)) {
                throw new \Exception("Failed to save generated file");
            }
            
            // Insert document record
            $sql = "INSERT INTO dokumen_generated (
                        template_dokumen_id, nama_file, file_path, data_variables,
                        generated_by, generated_for_siswa_id, generated_for_kelas_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $templateId,
                $fileName,
                $filePath,
                json_encode($variables),
                $generatedBy,
                $siswaId,
                $kelasId
            ]);
            
            $documentId = (int)$this->db->lastInsertId();
            
            $this->db->commit();
            $this->getLogger()->info("Document generated", [
                'template_id' => $templateId,
                'document_id' => $documentId,
                'file_name' => $fileName
            ]);
            
            return $documentId;
        } catch (\Exception $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error generating document", [
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Generate attendance document
     *
     * @param array $filters Filters for attendance data
     * @param int $generatedBy User ID
     * @return int|null Generated document ID
     */
    public function generateAbsensiDocument(array $filters, int $generatedBy): ?int
    {
        try {
            // Get attendance template
            $template = $this->getTemplateByType('absensi');
            if (!$template) {
                throw new \Exception("Attendance template not found");
            }
            
            // Get attendance data
            $attendanceData = $this->getAttendanceData($filters);
            
            $variables = [
                'tanggal_generate' => date('d/m/Y'),
                'periode' => $filters['periode'] ?? date('Y-m'),
                'kelas' => $filters['nama_kelas'] ?? 'Semua Kelas',
                'data_presensi' => $attendanceData,
                'total_siswa' => count($attendanceData),
                'total_pertemuan' => $this->getTotalMeetings($filters)
            ];
            
            return $this->generateDocument(
                $template['id'], 
                $variables, 
                $generatedBy, 
                null, 
                $filters['kelas_id'] ?? null
            );
        } catch (\Exception $e) {
            $this->getLogger()->error("Error generating attendance document", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Generate grade report document
     *
     * @param array $filters Filters for grade data
     * @param int $generatedBy User ID
     * @return int|null Generated document ID
     */
    public function generateLaporanNilaiDocument(array $filters, int $generatedBy): ?int
    {
        try {
            // Get grade report template
            $template = $this->getTemplateByType('laporan_nilai');
            if (!$template) {
                throw new \Exception("Grade report template not found");
            }
            
            // Get grade data
            $gradeData = $this->getGradeData($filters);
            
            $variables = [
                'tanggal_generate' => date('d/m/Y'),
                'periode' => $filters['periode'] ?? date('Y-m'),
                'siswa' => $filters['nama_siswa'] ?? 'Semua Siswa',
                'kelas' => $filters['nama_kelas'] ?? 'Semua Kelas',
                'data_nilai' => $gradeData,
                'rata_rata_keseluruhan' => $this->calculateOverallAverage($gradeData)
            ];
            
            return $this->generateDocument(
                $template['id'], 
                $variables, 
                $generatedBy, 
                $filters['siswa_id'] ?? null, 
                $filters['kelas_id'] ?? null
            );
        } catch (\Exception $e) {
            $this->getLogger()->error("Error generating grade report document", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Generate student data document
     *
     * @param int $siswaId Student ID
     * @param int $generatedBy User ID
     * @return int|null Generated document ID
     */
    public function generateDataSiswaDocument(int $siswaId, int $generatedBy): ?int
    {
        try {
            // Get student data template
            $template = $this->getTemplateByType('data_siswa');
            if (!$template) {
                throw new \Exception("Student data template not found");
            }
            
            // Get student data
            $studentData = $this->getStudentCompleteData($siswaId);
            if (!$studentData) {
                throw new \Exception("Student not found");
            }
            
            $variables = [
                'tanggal_generate' => date('d/m/Y'),
                'siswa' => $studentData['siswa'],
                'orang_tua' => $studentData['orang_tua'],
                'kelas_list' => $studentData['kelas'],
                'statistik' => $studentData['statistik']
            ];
            
            return $this->generateDocument(
                $template['id'], 
                $variables, 
                $generatedBy, 
                $siswaId
            );
        } catch (\Exception $e) {
            $this->getLogger()->error("Error generating student data document", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get template by type
     *
     * @param string $type Template type
     * @return array|null Template data
     */
    private function getTemplateByType(string $type): ?array
    {
        try {
            $sql = "SELECT * FROM template_dokumen WHERE jenis = ? AND status_aktif = 1 LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$type]);
            return $stmt->fetch();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching template by type", ['type' => $type, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Generate file name for document
     *
     * @param array $template Template data
     * @param array $variables Variables
     * @return string Generated file name
     */
    private function generateFileName(array $template, array $variables): string
    {
        $timestamp = date('Y-m-d_H-i-s');
        $baseName = strtolower(str_replace(' ', '_', $template['nama']));
        
        if (isset($variables['siswa']['nama_lengkap'])) {
            $studentName = strtolower(str_replace(' ', '_', $variables['siswa']['nama_lengkap']));
            return "{$baseName}_{$studentName}_{$timestamp}.html";
        }
        
        return "{$baseName}_{$timestamp}.html";
    }

    /**
     * Generate file path for document
     *
     * @param string $fileName File name
     * @return string File path
     */
    private function generateFilePath(string $fileName): string
    {
        $uploadDir = STORAGE_PATH . '/documents/generated';
        
        // Create directory if not exists
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        return $uploadDir . '/' . $fileName;
    }

    /**
     * Process template with variables
     *
     * @param string $templatePath Template file path
     * @param array $variables Variables to replace
     * @return string Processed content
     */
    private function processTemplate(string $templatePath, array $variables): string
    {
        $fullPath = STORAGE_PATH . '/templates/' . $templatePath;
        
        if (!file_exists($fullPath)) {
            throw new \Exception("Template file not found: {$templatePath}");
        }
        
        $content = file_get_contents($fullPath);
        
        // Simple variable replacement
        foreach ($variables as $key => $value) {
            if (is_array($value)) {
                // Handle array variables (like tables)
                $content = $this->processArrayVariable($content, $key, $value);
            } else {
                $content = str_replace("{{$key}}", $value, $content);
            }
        }
        
        return $content;
    }

    /**
     * Process array variables in template
     *
     * @param string $content Template content
     * @param string $key Variable key
     * @param array $data Array data
     * @return string Processed content
     */
    private function processArrayVariable(string $content, string $key, array $data): string
    {
        // Simple table generation for array data
        if (strpos($content, "{{$key}}") !== false) {
            $tableHtml = $this->generateTableFromArray($data);
            $content = str_replace("{{$key}}", $tableHtml, $content);
        }
        
        return $content;
    }

    /**
     * Generate HTML table from array data
     *
     * @param array $data Array data
     * @return string HTML table
     */
    private function generateTableFromArray(array $data): string
    {
        if (empty($data)) {
            return '<p>Tidak ada data</p>';
        }
        
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
        
        // Header
        $html .= '<thead><tr>';
        foreach (array_keys($data[0]) as $header) {
            $html .= '<th>' . ucfirst(str_replace('_', ' ', $header)) . '</th>';
        }
        $html .= '</tr></thead>';
        
        // Body
        $html .= '<tbody>';
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $cell) {
                $html .= '<td>' . htmlspecialchars($cell) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody></table>';
        
        return $html;
    }

    /**
     * Save generated file
     *
     * @param string $filePath File path
     * @param string $content File content
     * @return bool Success status
     */
    private function saveGeneratedFile(string $filePath, string $content): bool
    {
        return file_put_contents($filePath, $content) !== false;
    }

    // Additional helper methods would be implemented here for:
    // - getAttendanceData()
    // - getGradeData()
    // - getStudentCompleteData()
    // - calculateOverallAverage()
    // - getTotalMeetings()
    // etc.
}
