<?php

declare(strict_types=1);

/**
 * Siswa Module Routes Configuration
 *
 * Defines routes for the Siswa module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\Siswa\Config
 */

return [
    'name' => 'Siswa',
    'namespace' => 'Modules\\Siswa',
    'default_controller' => 'SiswaController',
    'default_action' => 'dashboard',

    'routes' => [
        'siswa' => [
            'controller' => 'SiswaController',
            'action' => 'dashboard'
        ],
        'siswa/dashboard' => [
            'controller' => 'SiswaController',
            'action' => 'dashboard'
        ],
        'siswa/profile' => [
            'controller' => 'SiswaController',
            'action' => 'profile'
        ],
        'siswa/profile/edit' => [
            'controller' => 'SiswaController',
            'action' => 'editProfile'
        ],
        'siswa/profile/update' => [
            'controller' => 'SiswaController',
            'action' => 'updateProfile'
        ],
        'siswa/jadwal' => [
            'controller' => 'SiswaController',
            'action' => 'jadwal'
        ],
        'siswa/nilai' => [
            'controller' => 'SiswaController',
            'action' => 'nilai'
        ],
        'siswa/presensi' => [
            'controller' => 'SiswaController',
            'action' => 'presensi'
        ],
        'siswa/tugas' => [
            'controller' => 'SiswaController',
            'action' => 'tugas'
        ],
        'siswa/pembayaran' => [
            'controller' => 'SiswaController',
            'action' => 'pembayaran'
        ],
        'siswa/diskusi' => [
            'controller' => 'DiskusiController',
            'action' => 'index'
        ],
        'siswa/diskusi/create' => [
            'controller' => 'DiskusiController',
            'action' => 'create'
        ],
        'siswa/diskusi/{id}' => [
            'controller' => 'DiskusiController',
            'action' => 'view'
        ],
        'siswa/diskusi/{id}/reply' => [
            'controller' => 'DiskusiController',
            'action' => 'reply'
        ]
    ]
];
