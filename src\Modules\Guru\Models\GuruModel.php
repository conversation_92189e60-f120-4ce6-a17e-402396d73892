<?php

declare(strict_types=1);

namespace Modules\Guru\Models;

use Core\Model;
use PDO;

/**
 * Guru Model
 *
 * Handles data operations for teacher management
 *
 * @package Modules\Guru\Models
 */
class GuruModel extends Model
{
    /**
     * Get teacher profile by user ID
     *
     * @param int $userId User ID
     * @return array|null Teacher profile data
     */
    public function getProfileByUserId(int $userId): ?array
    {
        try {
            $sql = "SELECT g.*, u.username, u.email,
                           GROUP_CONCAT(mp.nama SEPARATOR ', ') as mata_pelajaran
                    FROM guru g
                    JOIN users u ON g.user_id = u.id
                    LEFT JOIN guru_mata_pelajaran gmp ON g.id = gmp.guru_id
                    LEFT JOIN mata_pelajaran mp ON gmp.mata_pelajaran_id = mp.id
                    WHERE g.user_id = ? AND g.status_aktif = 1
                    GROUP BY g.id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching teacher profile", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get teacher classes
     *
     * @param int $guruId Teacher ID
     * @return array List of classes
     */
    public function getKelas(int $guruId): array
    {
        try {
            $sql = "SELECT kb.*, mp.nama as mata_pelajaran,
                           COUNT(pk.siswa_id) as jumlah_siswa
                    FROM kelas_bimbel kb
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    LEFT JOIN pendaftaran_kelas pk ON kb.id = pk.kelas_bimbel_id AND pk.status = 'aktif'
                    WHERE kb.guru_id = ? AND kb.status_aktif = 1
                    GROUP BY kb.id
                    ORDER BY kb.jadwal_hari, kb.jam_mulai";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$guruId]);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching teacher classes", ['guru_id' => $guruId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get class details with students
     *
     * @param int $kelasId Class ID
     * @param int $guruId Teacher ID (for security check)
     * @return array|null Class details
     */
    public function getDetailKelas(int $kelasId, int $guruId): ?array
    {
        try {
            $sql = "SELECT kb.*, mp.nama as mata_pelajaran, mp.kode as kode_mapel
                    FROM kelas_bimbel kb
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    WHERE kb.id = ? AND kb.guru_id = ? AND kb.status_aktif = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$kelasId, $guruId]);
            $kelas = $stmt->fetch();
            
            if (!$kelas) {
                return null;
            }
            
            // Get students in this class
            $sql = "SELECT s.*, pk.tanggal_daftar, pk.status as status_pendaftaran
                    FROM pendaftaran_kelas pk
                    JOIN siswa s ON pk.siswa_id = s.id
                    WHERE pk.kelas_bimbel_id = ? AND pk.status = 'aktif'
                    ORDER BY s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$kelasId]);
            $kelas['siswa'] = $stmt->fetchAll();
            
            return $kelas;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching class details", ['kelas_id' => $kelasId, 'guru_id' => $guruId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get lesson plans for teacher
     *
     * @param int $guruId Teacher ID
     * @param int|null $kelasId Optional class ID filter
     * @return array List of lesson plans
     */
    public function getRencanaPengajaran(int $guruId, ?int $kelasId = null): array
    {
        try {
            $sql = "SELECT rp.*, kb.nama as nama_kelas, mp.nama as mata_pelajaran
                    FROM rencana_pengajaran rp
                    JOIN kelas_bimbel kb ON rp.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    WHERE rp.guru_id = ?";
            
            $params = [$guruId];
            
            if ($kelasId) {
                $sql .= " AND rp.kelas_bimbel_id = ?";
                $params[] = $kelasId;
            }
            
            $sql .= " ORDER BY rp.tanggal_rencana DESC, rp.pertemuan_ke DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching lesson plans", ['guru_id' => $guruId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Create lesson plan
     *
     * @param array $data Lesson plan data
     * @return int|null The new lesson plan ID or null if failed
     */
    public function createRencanaPengajaran(array $data): ?int
    {
        try {
            $sql = "INSERT INTO rencana_pengajaran (
                        kelas_bimbel_id, guru_id, pertemuan_ke, tanggal_rencana,
                        topik, tujuan_pembelajaran, materi_pokok, metode_pembelajaran,
                        media_pembelajaran, evaluasi, tugas_rumah, catatan_guru, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['kelas_bimbel_id'],
                $data['guru_id'],
                $data['pertemuan_ke'],
                $data['tanggal_rencana'],
                $data['topik'],
                $data['tujuan_pembelajaran'],
                $data['materi_pokok'],
                $data['metode_pembelajaran'] ?? null,
                $data['media_pembelajaran'] ?? null,
                $data['evaluasi'] ?? null,
                $data['tugas_rumah'] ?? null,
                $data['catatan_guru'] ?? null,
                $data['status'] ?? 'draft'
            ]);

            if ($result) {
                $id = (int)$this->db->lastInsertId();
                $this->getLogger()->info("Lesson plan created", ['id' => $id, 'guru_id' => $data['guru_id']]);
                return $id;
            }

            return null;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error creating lesson plan", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Update lesson plan
     *
     * @param int $id Lesson plan ID
     * @param array $data Updated data
     * @param int $guruId Teacher ID (for security check)
     * @return bool Success status
     */
    public function updateRencanaPengajaran(int $id, array $data, int $guruId): bool
    {
        try {
            $sql = "UPDATE rencana_pengajaran SET 
                        tanggal_rencana = ?, topik = ?, tujuan_pembelajaran = ?,
                        materi_pokok = ?, metode_pembelajaran = ?, media_pembelajaran = ?,
                        evaluasi = ?, tugas_rumah = ?, catatan_guru = ?, status = ?,
                        updated_at = NOW()
                    WHERE id = ? AND guru_id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['tanggal_rencana'],
                $data['topik'],
                $data['tujuan_pembelajaran'],
                $data['materi_pokok'],
                $data['metode_pembelajaran'] ?? null,
                $data['media_pembelajaran'] ?? null,
                $data['evaluasi'] ?? null,
                $data['tugas_rumah'] ?? null,
                $data['catatan_guru'] ?? null,
                $data['status'] ?? 'draft',
                $id,
                $guruId
            ]);

            if ($result) {
                $this->getLogger()->info("Lesson plan updated", ['id' => $id, 'guru_id' => $guruId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error updating lesson plan", ['id' => $id, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get attendance records for teacher's classes
     *
     * @param int $guruId Teacher ID
     * @param string|null $tanggal Date filter
     * @param int|null $kelasId Class ID filter
     * @return array Attendance records
     */
    public function getPresensi(int $guruId, ?string $tanggal = null, ?int $kelasId = null): array
    {
        try {
            $sql = "SELECT p.*, s.nama_lengkap as nama_siswa, kb.nama as nama_kelas,
                           mp.nama as mata_pelajaran, rp.topik, rp.pertemuan_ke
                    FROM presensi p
                    JOIN siswa s ON p.siswa_id = s.id
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    LEFT JOIN rencana_pengajaran rp ON p.rencana_pengajaran_id = rp.id
                    WHERE kb.guru_id = ?";
            
            $params = [$guruId];
            
            if ($tanggal) {
                $sql .= " AND p.tanggal = ?";
                $params[] = $tanggal;
            }
            
            if ($kelasId) {
                $sql .= " AND p.kelas_bimbel_id = ?";
                $params[] = $kelasId;
            }
            
            $sql .= " ORDER BY p.tanggal DESC, kb.nama, s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching attendance", ['guru_id' => $guruId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Save attendance records
     *
     * @param array $attendanceData Attendance data
     * @param int $guruId Teacher ID
     * @return bool Success status
     */
    public function savePresensi(array $attendanceData, int $guruId): bool
    {
        try {
            $this->db->beginTransaction();
            
            foreach ($attendanceData as $data) {
                // Check if attendance already exists
                $sql = "SELECT id FROM presensi 
                        WHERE siswa_id = ? AND kelas_bimbel_id = ? AND tanggal = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$data['siswa_id'], $data['kelas_bimbel_id'], $data['tanggal']]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // Update existing record
                    $sql = "UPDATE presensi SET 
                                status = ?, jam_masuk = ?, jam_keluar = ?, catatan = ?,
                                updated_at = NOW()
                            WHERE id = ?";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([
                        $data['status'],
                        $data['jam_masuk'] ?? null,
                        $data['jam_keluar'] ?? null,
                        $data['catatan'] ?? null,
                        $existing['id']
                    ]);
                } else {
                    // Insert new record
                    $sql = "INSERT INTO presensi (
                                siswa_id, kelas_bimbel_id, rencana_pengajaran_id,
                                tanggal, jam_masuk, jam_keluar, status, catatan, created_by
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([
                        $data['siswa_id'],
                        $data['kelas_bimbel_id'],
                        $data['rencana_pengajaran_id'] ?? null,
                        $data['tanggal'],
                        $data['jam_masuk'] ?? null,
                        $data['jam_keluar'] ?? null,
                        $data['status'],
                        $data['catatan'] ?? null,
                        $guruId
                    ]);
                }
            }
            
            $this->db->commit();
            $this->getLogger()->info("Attendance saved", ['guru_id' => $guruId, 'count' => count($attendanceData)]);
            return true;
        } catch (\PDOException $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error saving attendance", ['guru_id' => $guruId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get teacher dashboard statistics
     *
     * @param int $guruId Teacher ID
     * @return array Statistics data
     */
    public function getDashboardStats(int $guruId): array
    {
        try {
            $stats = [];
            
            // Total kelas aktif
            $sql = "SELECT COUNT(*) as total FROM kelas_bimbel WHERE guru_id = ? AND status_aktif = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$guruId]);
            $stats['total_kelas'] = $stmt->fetchColumn();
            
            // Total siswa aktif
            $sql = "SELECT COUNT(DISTINCT pk.siswa_id) as total
                    FROM pendaftaran_kelas pk
                    JOIN kelas_bimbel kb ON pk.kelas_bimbel_id = kb.id
                    WHERE kb.guru_id = ? AND pk.status = 'aktif'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$guruId]);
            $stats['total_siswa'] = $stmt->fetchColumn();
            
            // Rencana pengajaran pending
            $sql = "SELECT COUNT(*) as total FROM rencana_pengajaran 
                    WHERE guru_id = ? AND status = 'draft'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$guruId]);
            $stats['rencana_pending'] = $stmt->fetchColumn();
            
            // Presensi hari ini
            $sql = "SELECT COUNT(*) as total
                    FROM presensi p
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    WHERE kb.guru_id = ? AND p.tanggal = CURDATE()";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$guruId]);
            $stats['presensi_hari_ini'] = $stmt->fetchColumn();
            
            return $stats;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching dashboard statistics", ['guru_id' => $guruId, 'error' => $e->getMessage()]);
            return [];
        }
    }
}
