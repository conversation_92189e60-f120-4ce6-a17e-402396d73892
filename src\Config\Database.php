<?php

declare(strict_types=1);

namespace Config;

/**
 * Database Configuration Class
 *
 * Contains database connection settings.
 * Follows PSR standards for configuration management.
 *
 * @package Config
 */
final class Database
{
    /**
     * Database driver
     */
    public const DRIVER = 'mysql';

    /**
     * Database host
     */
    public const HOST = 'localhost';

    /**
     * Database port (empty for default)
     */
    public const PORT = '';

    /**
     * Database name
     */
    public const DATABASE = USE_DATABASE ? 'db_bimbel_online' : '';

    /**
     * Database username
     */
    public const USERNAME = USE_DATABASE ? 'root' : '';

    /**
     * Database password
     */
    public const PASSWORD = USE_DATABASE ? '' : '';

    /**
     * Database character set
     */
    public const CHARSET = 'utf8mb4';
}
