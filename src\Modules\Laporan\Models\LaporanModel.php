<?php

declare(strict_types=1);

namespace Modules\Laporan\Models;

use Core\Model;
use PDO;

/**
 * Laporan Model
 *
 * Handles data operations for report management
 *
 * @package Modules\Laporan\Models
 */
class LaporanModel extends Model
{
    /**
     * Create progress report
     *
     * @param array $data Report data
     * @return int|null The new report ID or null if failed
     */
    public function createLaporanPerkembangan(array $data): ?int
    {
        try {
            $sql = "INSERT INTO laporan_perkembangan (
                        siswa_id, kelas_bimbel_id, periode, rata_rata_nilai,
                        total_kehadiran, total_pertemuan, persentase_kehadiran,
                        catatan_guru, rekomendasi, tanggal_laporan, status, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['siswa_id'],
                $data['kelas_bimbel_id'],
                $data['periode'],
                $data['rata_rata_nilai'] ?? null,
                $data['total_kehadiran'] ?? 0,
                $data['total_pertemuan'] ?? 0,
                $data['persentase_kehadiran'] ?? 0,
                $data['catatan_guru'] ?? null,
                $data['rekomendasi'] ?? null,
                $data['tanggal_laporan'],
                $data['status'] ?? 'draft',
                $data['created_by']
            ]);

            if ($result) {
                $id = (int)$this->db->lastInsertId();
                $this->getLogger()->info("Progress report created", ['id' => $id, 'siswa_id' => $data['siswa_id']]);
                return $id;
            }

            return null;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error creating progress report", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Auto-generate progress report for a student
     *
     * @param int $siswaId Student ID
     * @param int $kelasId Class ID
     * @param string $periode Period (semester-year)
     * @param int $createdBy User ID who creates the report
     * @return int|null The new report ID or null if failed
     */
    public function autoGenerateLaporanPerkembangan(int $siswaId, int $kelasId, string $periode, int $createdBy): ?int
    {
        try {
            // Calculate statistics
            $stats = $this->calculateStudentStats($siswaId, $kelasId, $periode);
            
            $data = [
                'siswa_id' => $siswaId,
                'kelas_bimbel_id' => $kelasId,
                'periode' => $periode,
                'rata_rata_nilai' => $stats['rata_rata_nilai'],
                'total_kehadiran' => $stats['total_kehadiran'],
                'total_pertemuan' => $stats['total_pertemuan'],
                'persentase_kehadiran' => $stats['persentase_kehadiran'],
                'catatan_guru' => $this->generateCatatanGuru($stats),
                'rekomendasi' => $this->generateRekomendasi($stats),
                'tanggal_laporan' => date('Y-m-d'),
                'status' => 'final',
                'created_by' => $createdBy
            ];

            return $this->createLaporanPerkembangan($data);
        } catch (\Exception $e) {
            $this->getLogger()->error("Error auto-generating progress report", [
                'siswa_id' => $siswaId,
                'kelas_id' => $kelasId,
                'periode' => $periode,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Calculate student statistics for a period
     *
     * @param int $siswaId Student ID
     * @param int $kelasId Class ID
     * @param string $periode Period
     * @return array Statistics
     */
    private function calculateStudentStats(int $siswaId, int $kelasId, string $periode): array
    {
        $stats = [
            'rata_rata_nilai' => 0,
            'total_kehadiran' => 0,
            'total_pertemuan' => 0,
            'persentase_kehadiran' => 0
        ];

        try {
            // Calculate average grade
            $sql = "SELECT AVG(nilai) as rata_rata 
                    FROM nilai 
                    WHERE siswa_id = ? AND kelas_bimbel_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId, $kelasId]);
            $result = $stmt->fetch();
            $stats['rata_rata_nilai'] = round($result['rata_rata'] ?? 0, 2);

            // Calculate attendance
            $sql = "SELECT 
                        COUNT(*) as total_pertemuan,
                        SUM(CASE WHEN status = 'hadir' THEN 1 ELSE 0 END) as total_hadir
                    FROM presensi 
                    WHERE siswa_id = ? AND kelas_bimbel_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId, $kelasId]);
            $result = $stmt->fetch();
            
            $stats['total_pertemuan'] = $result['total_pertemuan'] ?? 0;
            $stats['total_kehadiran'] = $result['total_hadir'] ?? 0;
            
            if ($stats['total_pertemuan'] > 0) {
                $stats['persentase_kehadiran'] = round(($stats['total_kehadiran'] / $stats['total_pertemuan']) * 100, 2);
            }

            return $stats;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error calculating student stats", ['error' => $e->getMessage()]);
            return $stats;
        }
    }

    /**
     * Generate teacher notes based on statistics
     *
     * @param array $stats Student statistics
     * @return string Generated notes
     */
    private function generateCatatanGuru(array $stats): string
    {
        $catatan = [];

        // Performance notes
        if ($stats['rata_rata_nilai'] >= 85) {
            $catatan[] = "Siswa menunjukkan prestasi akademik yang sangat baik dengan rata-rata nilai {$stats['rata_rata_nilai']}.";
        } elseif ($stats['rata_rata_nilai'] >= 75) {
            $catatan[] = "Siswa memiliki prestasi akademik yang baik dengan rata-rata nilai {$stats['rata_rata_nilai']}.";
        } elseif ($stats['rata_rata_nilai'] >= 65) {
            $catatan[] = "Siswa menunjukkan prestasi akademik yang cukup dengan rata-rata nilai {$stats['rata_rata_nilai']}.";
        } else {
            $catatan[] = "Siswa perlu meningkatkan prestasi akademik. Rata-rata nilai saat ini {$stats['rata_rata_nilai']}.";
        }

        // Attendance notes
        if ($stats['persentase_kehadiran'] >= 90) {
            $catatan[] = "Tingkat kehadiran sangat baik ({$stats['persentase_kehadiran']}%).";
        } elseif ($stats['persentase_kehadiran'] >= 80) {
            $catatan[] = "Tingkat kehadiran baik ({$stats['persentase_kehadiran']}%).";
        } elseif ($stats['persentase_kehadiran'] >= 70) {
            $catatan[] = "Tingkat kehadiran cukup ({$stats['persentase_kehadiran']}%).";
        } else {
            $catatan[] = "Tingkat kehadiran perlu ditingkatkan ({$stats['persentase_kehadiran']}%).";
        }

        return implode(' ', $catatan);
    }

    /**
     * Generate recommendations based on statistics
     *
     * @param array $stats Student statistics
     * @return string Generated recommendations
     */
    private function generateRekomendasi(array $stats): string
    {
        $rekomendasi = [];

        if ($stats['rata_rata_nilai'] < 75) {
            $rekomendasi[] = "Disarankan untuk menambah waktu belajar di rumah dan mengerjakan latihan soal tambahan.";
        }

        if ($stats['persentase_kehadiran'] < 80) {
            $rekomendasi[] = "Perlu meningkatkan kehadiran untuk mengikuti semua materi pembelajaran.";
        }

        if ($stats['rata_rata_nilai'] >= 85 && $stats['persentase_kehadiran'] >= 90) {
            $rekomendasi[] = "Pertahankan prestasi yang baik dan terus tingkatkan kemampuan.";
        }

        if (empty($rekomendasi)) {
            $rekomendasi[] = "Terus semangat belajar dan jaga konsistensi dalam mengikuti pembelajaran.";
        }

        return implode(' ', $rekomendasi);
    }

    /**
     * Get progress reports
     *
     * @param array $filters Filters (siswa_id, kelas_id, periode, status)
     * @return array List of reports
     */
    public function getLaporanPerkembangan(array $filters = []): array
    {
        try {
            $sql = "SELECT lp.*, s.nama_lengkap as nama_siswa, s.nis,
                           kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           g.nama_lengkap as nama_guru, ot.nama_lengkap as nama_orang_tua,
                           u.username as created_by_username
                    FROM laporan_perkembangan lp
                    JOIN siswa s ON lp.siswa_id = s.id
                    JOIN kelas_bimbel kb ON lp.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    JOIN guru g ON kb.guru_id = g.id
                    JOIN orang_tua ot ON s.orang_tua_id = ot.id
                    JOIN users u ON lp.created_by = u.id
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['siswa_id'])) {
                $sql .= " AND lp.siswa_id = ?";
                $params[] = $filters['siswa_id'];
            }
            
            if (!empty($filters['kelas_id'])) {
                $sql .= " AND lp.kelas_bimbel_id = ?";
                $params[] = $filters['kelas_id'];
            }
            
            if (!empty($filters['periode'])) {
                $sql .= " AND lp.periode = ?";
                $params[] = $filters['periode'];
            }
            
            if (!empty($filters['status'])) {
                $sql .= " AND lp.status = ?";
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['orang_tua_id'])) {
                $sql .= " AND s.orang_tua_id = ?";
                $params[] = $filters['orang_tua_id'];
            }
            
            $sql .= " ORDER BY lp.periode DESC, lp.tanggal_laporan DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching progress reports", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Update report status
     *
     * @param int $id Report ID
     * @param string $status New status
     * @return bool Success status
     */
    public function updateStatus(int $id, string $status): bool
    {
        try {
            $sql = "UPDATE laporan_perkembangan SET status = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$status, $id]);

            if ($result) {
                $this->getLogger()->info("Report status updated", ['id' => $id, 'status' => $status]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error updating report status", ['id' => $id, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Send report to parent (mark as sent and create notification)
     *
     * @param int $reportId Report ID
     * @return bool Success status
     */
    public function sendToParent(int $reportId): bool
    {
        try {
            $this->db->beginTransaction();
            
            // Get report details
            $sql = "SELECT lp.*, s.nama_lengkap as nama_siswa, s.orang_tua_id,
                           ot.user_id as parent_user_id, kb.nama as nama_kelas
                    FROM laporan_perkembangan lp
                    JOIN siswa s ON lp.siswa_id = s.id
                    JOIN orang_tua ot ON s.orang_tua_id = ot.id
                    JOIN kelas_bimbel kb ON lp.kelas_bimbel_id = kb.id
                    WHERE lp.id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$reportId]);
            $report = $stmt->fetch();
            
            if (!$report) {
                throw new \Exception("Report not found");
            }
            
            // Update report status
            $this->updateStatus($reportId, 'terkirim');
            
            // Create notification for parent
            $sql = "INSERT INTO notifikasi (user_id, judul, pesan, jenis, kategori, action_url, data_payload)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $report['parent_user_id'],
                'Laporan Perkembangan Tersedia',
                "Laporan perkembangan untuk {$report['nama_siswa']} di kelas {$report['nama_kelas']} periode {$report['periode']} telah tersedia.",
                'info',
                'pengumuman',
                "/orang-tua/laporan/{$reportId}",
                json_encode(['report_id' => $reportId, 'siswa_id' => $report['siswa_id']])
            ]);
            
            $this->db->commit();
            $this->getLogger()->info("Report sent to parent", ['report_id' => $reportId, 'parent_user_id' => $report['parent_user_id']]);
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error sending report to parent", ['report_id' => $reportId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Generate attendance report
     *
     * @param array $filters Filters for the report
     * @return array Attendance data
     */
    public function generateLaporanPresensi(array $filters): array
    {
        try {
            $sql = "SELECT s.nama_lengkap as nama_siswa, s.nis,
                           kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           p.tanggal, p.status, p.jam_masuk, p.jam_keluar, p.catatan
                    FROM presensi p
                    JOIN siswa s ON p.siswa_id = s.id
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['siswa_id'])) {
                $sql .= " AND p.siswa_id = ?";
                $params[] = $filters['siswa_id'];
            }
            
            if (!empty($filters['kelas_id'])) {
                $sql .= " AND p.kelas_bimbel_id = ?";
                $params[] = $filters['kelas_id'];
            }
            
            if (!empty($filters['tanggal_mulai'])) {
                $sql .= " AND p.tanggal >= ?";
                $params[] = $filters['tanggal_mulai'];
            }
            
            if (!empty($filters['tanggal_selesai'])) {
                $sql .= " AND p.tanggal <= ?";
                $params[] = $filters['tanggal_selesai'];
            }
            
            $sql .= " ORDER BY p.tanggal DESC, s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error generating attendance report", ['error' => $e->getMessage()]);
            return [];
        }
    }
}
