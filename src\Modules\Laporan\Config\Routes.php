<?php

declare(strict_types=1);

/**
 * Laporan Module Routes Configuration
 *
 * Defines routes for the Laporan module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\Laporan\Config
 */

return [
    'name' => 'Laporan',
    'namespace' => 'Modules\\Laporan',
    'default_controller' => 'LaporanController',
    'default_action' => 'index',

    'routes' => [
        'laporan' => [
            'controller' => 'LaporanController',
            'action' => 'index'
        ],
        'laporan/perkembangan' => [
            'controller' => 'PerkembanganController',
            'action' => 'index'
        ],
        'laporan/perkembangan/create' => [
            'controller' => 'PerkembanganController',
            'action' => 'create'
        ],
        'laporan/perkembangan/{id}' => [
            'controller' => 'PerkembanganController',
            'action' => 'detail'
        ],
        'laporan/perkembangan/{id}/edit' => [
            'controller' => 'PerkembanganController',
            'action' => 'edit'
        ],
        'laporan/perkembangan/{id}/update' => [
            'controller' => 'PerkembanganController',
            'action' => 'update'
        ],
        'laporan/perkembangan/{id}/send' => [
            'controller' => 'PerkembanganController',
            'action' => 'send'
        ],
        'laporan/perkembangan/{id}/download' => [
            'controller' => 'PerkembanganController',
            'action' => 'download'
        ],
        'laporan/presensi' => [
            'controller' => 'PresensiController',
            'action' => 'index'
        ],
        'laporan/presensi/generate' => [
            'controller' => 'PresensiController',
            'action' => 'generate'
        ],
        'laporan/presensi/download' => [
            'controller' => 'PresensiController',
            'action' => 'download'
        ],
        'laporan/nilai' => [
            'controller' => 'NilaiController',
            'action' => 'index'
        ],
        'laporan/nilai/generate' => [
            'controller' => 'NilaiController',
            'action' => 'generate'
        ],
        'laporan/nilai/download' => [
            'controller' => 'NilaiController',
            'action' => 'download'
        ],
        'laporan/pembayaran' => [
            'controller' => 'PembayaranController',
            'action' => 'index'
        ],
        'laporan/pembayaran/generate' => [
            'controller' => 'PembayaranController',
            'action' => 'generate'
        ],
        'laporan/pembayaran/download' => [
            'controller' => 'PembayaranController',
            'action' => 'download'
        ],
        'laporan/auto-generate' => [
            'controller' => 'AutoGenerateController',
            'action' => 'index'
        ],
        'laporan/auto-generate/run' => [
            'controller' => 'AutoGenerateController',
            'action' => 'run'
        ],
        'laporan/template' => [
            'controller' => 'TemplateController',
            'action' => 'index'
        ],
        'laporan/template/create' => [
            'controller' => 'TemplateController',
            'action' => 'create'
        ],
        'laporan/template/{id}/edit' => [
            'controller' => 'TemplateController',
            'action' => 'edit'
        ]
    ]
];
