<?php

declare(strict_types=1);

namespace Modules\Pembayaran\Models;

use Core\Model;
use PDO;

/**
 * Pembayaran Model
 *
 * Handles data operations for payment management
 *
 * @package Modules\Pembayaran\Models
 */
class PembayaranModel extends Model
{
    /**
     * Generate monthly bills for all active students
     *
     * @param string $bulanTahun Month-Year (YYYY-MM)
     * @return int Number of bills generated
     */
    public function generateTagihanBulanan(string $bulanTahun): int
    {
        try {
            $this->db->beginTransaction();
            $count = 0;
            
            // Get all active student enrollments
            $sql = "SELECT pk.siswa_id, pk.kelas_bimbel_id, kb.biaya_per_bulan,
                           s.nama_lengkap as nama_siswa, kb.nama as nama_kelas
                    FROM pendaftaran_kelas pk
                    JOIN siswa s ON pk.siswa_id = s.id
                    JOIN kelas_bimbel kb ON pk.kelas_bimbel_id = kb.id
                    WHERE pk.status = 'aktif' AND s.status_aktif = 1 AND kb.status_aktif = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $enrollments = $stmt->fetchAll();
            
            foreach ($enrollments as $enrollment) {
                // Check if bill already exists
                $sql = "SELECT id FROM pembayaran 
                        WHERE siswa_id = ? AND kelas_bimbel_id = ? AND bulan_tahun = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$enrollment['siswa_id'], $enrollment['kelas_bimbel_id'], $bulanTahun]);
                
                if (!$stmt->fetch()) {
                    // Create new bill
                    $tanggalJatuhTempo = date('Y-m-t', strtotime($bulanTahun . '-01'));
                    
                    $sql = "INSERT INTO pembayaran (
                                siswa_id, kelas_bimbel_id, bulan_tahun, jumlah_tagihan,
                                tanggal_jatuh_tempo, status
                            ) VALUES (?, ?, ?, ?, ?, 'belum_bayar')";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([
                        $enrollment['siswa_id'],
                        $enrollment['kelas_bimbel_id'],
                        $bulanTahun,
                        $enrollment['biaya_per_bulan'],
                        $tanggalJatuhTempo
                    ]);
                    
                    $count++;
                }
            }
            
            $this->db->commit();
            $this->getLogger()->info("Monthly bills generated", ['month' => $bulanTahun, 'count' => $count]);
            return $count;
        } catch (\PDOException $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error generating monthly bills", ['month' => $bulanTahun, 'error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Get payment records with filters
     *
     * @param array $filters Filters (siswa_id, kelas_id, status, bulan_tahun, orang_tua_id)
     * @return array Payment records
     */
    public function getPembayaran(array $filters = []): array
    {
        try {
            $sql = "SELECT p.*, s.nama_lengkap as nama_siswa, s.nis,
                           kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           ot.nama_lengkap as nama_orang_tua, ot.no_telepon as telepon_orang_tua,
                           u_ot.email as email_orang_tua
                    FROM pembayaran p
                    JOIN siswa s ON p.siswa_id = s.id
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    JOIN orang_tua ot ON s.orang_tua_id = ot.id
                    JOIN users u_ot ON ot.user_id = u_ot.id
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['siswa_id'])) {
                $sql .= " AND p.siswa_id = ?";
                $params[] = $filters['siswa_id'];
            }
            
            if (!empty($filters['kelas_id'])) {
                $sql .= " AND p.kelas_bimbel_id = ?";
                $params[] = $filters['kelas_id'];
            }
            
            if (!empty($filters['status'])) {
                $sql .= " AND p.status = ?";
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['bulan_tahun'])) {
                $sql .= " AND p.bulan_tahun = ?";
                $params[] = $filters['bulan_tahun'];
            }
            
            if (!empty($filters['orang_tua_id'])) {
                $sql .= " AND s.orang_tua_id = ?";
                $params[] = $filters['orang_tua_id'];
            }
            
            $sql .= " ORDER BY p.tanggal_jatuh_tempo DESC, s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching payments", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get overdue payments for reminder
     *
     * @param int $daysBefore Days before due date to send reminder
     * @return array Overdue payments
     */
    public function getOverduePayments(int $daysBefore = 3): array
    {
        try {
            $sql = "SELECT p.*, s.nama_lengkap as nama_siswa, s.nis,
                           kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           ot.nama_lengkap as nama_orang_tua, ot.no_telepon as telepon_orang_tua,
                           u_ot.email as email_orang_tua, u_ot.id as parent_user_id
                    FROM pembayaran p
                    JOIN siswa s ON p.siswa_id = s.id
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    JOIN orang_tua ot ON s.orang_tua_id = ot.id
                    JOIN users u_ot ON ot.user_id = u_ot.id
                    WHERE p.status IN ('belum_bayar', 'sebagian') 
                    AND p.tanggal_jatuh_tempo <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
                    ORDER BY p.tanggal_jatuh_tempo ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$daysBefore]);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching overdue payments", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Send payment reminder
     *
     * @param int $pembayaranId Payment ID
     * @param string $jenisReminder Reminder type (email, sms, whatsapp, notifikasi)
     * @param string $pesan Custom message
     * @return bool Success status
     */
    public function sendReminder(int $pembayaranId, string $jenisReminder, string $pesan = ''): bool
    {
        try {
            $this->db->beginTransaction();
            
            // Get payment details
            $sql = "SELECT p.*, s.nama_lengkap as nama_siswa,
                           kb.nama as nama_kelas, ot.nama_lengkap as nama_orang_tua,
                           u_ot.email as email_orang_tua, u_ot.id as parent_user_id
                    FROM pembayaran p
                    JOIN siswa s ON p.siswa_id = s.id
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN orang_tua ot ON s.orang_tua_id = ot.id
                    JOIN users u_ot ON ot.user_id = u_ot.id
                    WHERE p.id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pembayaranId]);
            $payment = $stmt->fetch();
            
            if (!$payment) {
                throw new \Exception("Payment not found");
            }
            
            // Generate default message if not provided
            if (empty($pesan)) {
                $pesan = $this->generateReminderMessage($payment);
            }
            
            // Insert reminder record
            $sql = "INSERT INTO pengingat_pembayaran (
                        pembayaran_id, jenis_pengingat, tanggal_kirim, status, pesan
                    ) VALUES (?, ?, NOW(), 'pending', ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pembayaranId, $jenisReminder, $pesan]);
            $reminderId = $this->db->lastInsertId();
            
            // Send reminder based on type
            $success = false;
            switch ($jenisReminder) {
                case 'email':
                    $success = $this->sendEmailReminder($payment, $pesan);
                    break;
                case 'sms':
                    $success = $this->sendSmsReminder($payment, $pesan);
                    break;
                case 'whatsapp':
                    $success = $this->sendWhatsappReminder($payment, $pesan);
                    break;
                case 'notifikasi':
                    $success = $this->sendNotificationReminder($payment, $pesan);
                    break;
            }
            
            // Update reminder status
            $status = $success ? 'terkirim' : 'gagal';
            $sql = "UPDATE pengingat_pembayaran SET status = ? WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$status, $reminderId]);
            
            $this->db->commit();
            $this->getLogger()->info("Payment reminder sent", [
                'pembayaran_id' => $pembayaranId,
                'jenis' => $jenisReminder,
                'status' => $status
            ]);
            
            return $success;
        } catch (\Exception $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error sending payment reminder", [
                'pembayaran_id' => $pembayaranId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Generate reminder message
     *
     * @param array $payment Payment data
     * @return string Generated message
     */
    private function generateReminderMessage(array $payment): string
    {
        $dueDate = date('d/m/Y', strtotime($payment['tanggal_jatuh_tempo']));
        $amount = number_format($payment['jumlah_tagihan'], 0, ',', '.');
        
        $message = "Yth. {$payment['nama_orang_tua']},\n\n";
        $message .= "Kami ingatkan bahwa pembayaran untuk:\n";
        $message .= "- Siswa: {$payment['nama_siswa']}\n";
        $message .= "- Kelas: {$payment['nama_kelas']}\n";
        $message .= "- Periode: {$payment['bulan_tahun']}\n";
        $message .= "- Jumlah: Rp {$amount}\n";
        $message .= "- Jatuh tempo: {$dueDate}\n\n";
        
        if ($payment['status'] === 'belum_bayar') {
            $message .= "Belum dilakukan pembayaran. ";
        } elseif ($payment['status'] === 'sebagian') {
            $paid = number_format($payment['jumlah_dibayar'], 0, ',', '.');
            $remaining = number_format($payment['jumlah_tagihan'] - $payment['jumlah_dibayar'], 0, ',', '.');
            $message .= "Sudah dibayar Rp {$paid}, sisa Rp {$remaining}. ";
        }
        
        $message .= "Mohon segera melakukan pembayaran.\n\n";
        $message .= "Terima kasih.\n";
        $message .= "Bimbel Online";
        
        return $message;
    }

    /**
     * Send email reminder
     *
     * @param array $payment Payment data
     * @param string $message Message content
     * @return bool Success status
     */
    private function sendEmailReminder(array $payment, string $message): bool
    {
        // TODO: Implement email sending logic
        // This would integrate with email service (PHPMailer, etc.)
        $this->getLogger()->info("Email reminder would be sent", [
            'to' => $payment['email_orang_tua'],
            'subject' => 'Pengingat Pembayaran Bimbel'
        ]);
        return true; // Simulate success for now
    }

    /**
     * Send SMS reminder
     *
     * @param array $payment Payment data
     * @param string $message Message content
     * @return bool Success status
     */
    private function sendSmsReminder(array $payment, string $message): bool
    {
        // TODO: Implement SMS sending logic
        // This would integrate with SMS gateway
        $this->getLogger()->info("SMS reminder would be sent", [
            'to' => $payment['telepon_orang_tua']
        ]);
        return true; // Simulate success for now
    }

    /**
     * Send WhatsApp reminder
     *
     * @param array $payment Payment data
     * @param string $message Message content
     * @return bool Success status
     */
    private function sendWhatsappReminder(array $payment, string $message): bool
    {
        // TODO: Implement WhatsApp sending logic
        // This would integrate with WhatsApp Business API
        $this->getLogger()->info("WhatsApp reminder would be sent", [
            'to' => $payment['telepon_orang_tua']
        ]);
        return true; // Simulate success for now
    }

    /**
     * Send notification reminder
     *
     * @param array $payment Payment data
     * @param string $message Message content
     * @return bool Success status
     */
    private function sendNotificationReminder(array $payment, string $message): bool
    {
        try {
            $sql = "INSERT INTO notifikasi (user_id, judul, pesan, jenis, kategori, action_url, data_payload)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $payment['parent_user_id'],
                'Pengingat Pembayaran',
                $message,
                'warning',
                'pembayaran',
                "/orang-tua/pembayaran/{$payment['id']}",
                json_encode(['pembayaran_id' => $payment['id']])
            ]);
            return true;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error sending notification reminder", ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Process payment
     *
     * @param int $pembayaranId Payment ID
     * @param array $paymentData Payment data
     * @return bool Success status
     */
    public function processPayment(int $pembayaranId, array $paymentData): bool
    {
        try {
            $this->db->beginTransaction();
            
            // Get current payment
            $sql = "SELECT * FROM pembayaran WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pembayaranId]);
            $payment = $stmt->fetch();
            
            if (!$payment) {
                throw new \Exception("Payment not found");
            }
            
            $newPaidAmount = $payment['jumlah_dibayar'] + $paymentData['jumlah_dibayar'];
            $newStatus = $this->determinePaymentStatus($newPaidAmount, $payment['jumlah_tagihan']);
            
            // Update payment
            $sql = "UPDATE pembayaran SET 
                        jumlah_dibayar = ?, 
                        tanggal_pembayaran = ?, 
                        status = ?, 
                        metode_pembayaran = ?, 
                        bukti_pembayaran = ?, 
                        catatan = ?,
                        updated_at = NOW()
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $newPaidAmount,
                $paymentData['tanggal_pembayaran'] ?? date('Y-m-d H:i:s'),
                $newStatus,
                $paymentData['metode_pembayaran'] ?? null,
                $paymentData['bukti_pembayaran'] ?? null,
                $paymentData['catatan'] ?? null,
                $pembayaranId
            ]);
            
            $this->db->commit();
            $this->getLogger()->info("Payment processed", [
                'pembayaran_id' => $pembayaranId,
                'amount' => $paymentData['jumlah_dibayar'],
                'new_status' => $newStatus
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error processing payment", [
                'pembayaran_id' => $pembayaranId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Determine payment status based on amounts
     *
     * @param float $paidAmount Paid amount
     * @param float $totalAmount Total amount
     * @return string Payment status
     */
    private function determinePaymentStatus(float $paidAmount, float $totalAmount): string
    {
        if ($paidAmount >= $totalAmount) {
            return 'lunas';
        } elseif ($paidAmount > 0) {
            return 'sebagian';
        } else {
            return 'belum_bayar';
        }
    }

    /**
     * Auto-send reminders for overdue payments
     *
     * @return int Number of reminders sent
     */
    public function autoSendReminders(): int
    {
        try {
            $reminderDays = 3; // Get from settings
            $overduePayments = $this->getOverduePayments($reminderDays);
            $count = 0;
            
            foreach ($overduePayments as $payment) {
                // Check if reminder was already sent today
                $sql = "SELECT id FROM pengingat_pembayaran 
                        WHERE pembayaran_id = ? AND DATE(tanggal_kirim) = CURDATE()";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$payment['id']]);
                
                if (!$stmt->fetch()) {
                    // Send notification reminder (most reliable)
                    if ($this->sendReminder($payment['id'], 'notifikasi')) {
                        $count++;
                    }
                }
            }
            
            $this->getLogger()->info("Auto reminders sent", ['count' => $count]);
            return $count;
        } catch (\Exception $e) {
            $this->getLogger()->error("Error auto-sending reminders", ['error' => $e->getMessage()]);
            return 0;
        }
    }
}
