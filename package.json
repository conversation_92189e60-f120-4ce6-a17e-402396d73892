{"name": "bimbel-online", "version": "1.0.0", "description": "Sistem Bimbel Online - Platform manajemen bimbingan belajar yang komprehensif dengan fitur pelaporan ke orang tua, pen<PERSON><PERSON> pembayaran, re<PERSON><PERSON> penga<PERSON>, template dokumen, dan sistem diskusi untuk operasional bimbel yang efektif dan efisien.", "scripts": {"dev": "npx tailwindcss -i ./resources/css/input.css -o ./public/css/styles.css --watch && npm run fa:copy", "build": "npx tailwindcss -i ./resources/css/input.css -o ./public/css/styles.css --minify", "fa:copy": "ncp node_modules/@fortawesome/fontawesome-free/webfonts public/webfonts && ncp node_modules/@fortawesome/fontawesome-free/css/all.min.css public/css/all.min.css", "postinstall": "npm run fa:copy"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.1", "ncp": "^2.0.0"}, "devDependencies": {"tailwindcss": "^3.3.2", "autoprefixer": "^10.4.14", "postcss": "^8.4.23", "postcss-cli": "^10.1.0"}}