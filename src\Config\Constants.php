<?php

declare(strict_types=1);

namespace Config;

/**
 * Constants Class
 *
 * Defines and initializes application constants.
 * Follows PSR standards for configuration management.
 *
 * @package Config
 */
class Constants
{
    /**
     * Initialize application constants
     *
     * @param string $rootPath The root path of the application
     * @return void
     */
    public static function init(string $rootPath): void
    {
        // Detect environment (local, hosting, virtual host)
        self::detectEnvironment();
        // Define paths
        if (!defined('ROOT_PATH')) define('ROOT_PATH', $rootPath);
        if (!defined('SRC_PATH')) define('SRC_PATH', ROOT_PATH . '/src');
        if (!defined('STORAGE_PATH')) define('STORAGE_PATH', ROOT_PATH . '/storage');
        if (!defined('PUBLIC_PATH')) define('PUBLIC_PATH', ROOT_PATH . '/public');
        if (!defined('RESOURCES_PATH')) define('RESOURCES_PATH', ROOT_PATH . '/resources');

        // Log paths
        if (!defined('LOGS_PATH')) define('LOGS_PATH', STORAGE_PATH . '/logs');
        if (!defined('PSR_LOGS_PATH')) define('PSR_LOGS_PATH', LOGS_PATH . '/psr');
        if (!defined('SYSTEM_LOGS_PATH')) define('SYSTEM_LOGS_PATH', LOGS_PATH . '/system');

        // PSR log categories
        if (!defined('PSR_SYSTEM')) define('PSR_SYSTEM', PSR_LOGS_PATH . '/system');
        if (!defined('PSR_ERROR')) define('PSR_ERROR', PSR_LOGS_PATH . '/error');
        if (!defined('PSR_DEBUG')) define('PSR_DEBUG', PSR_LOGS_PATH . '/debug');
        if (!defined('PSR_ACCESS')) define('PSR_ACCESS', PSR_LOGS_PATH . '/access');
        if (!defined('PSR_QUERY')) define('PSR_QUERY', PSR_LOGS_PATH . '/query');
        if (!defined('PSR_AUTH')) define('PSR_AUTH', PSR_LOGS_PATH . '/auth');

        // PSR log subcategories
        if (!defined('PSR_SYSTEM_ERROR')) define('PSR_SYSTEM_ERROR', PSR_SYSTEM . '/error');
        if (!defined('PSR_SYSTEM_DEBUG')) define('PSR_SYSTEM_DEBUG', PSR_SYSTEM . '/debug');
        if (!defined('PSR_SYSTEM_INFO')) define('PSR_SYSTEM_INFO', PSR_SYSTEM . '/info');

        if (!defined('PSR_ERROR_EMERGENCY')) define('PSR_ERROR_EMERGENCY', PSR_ERROR . '/emergency');
        if (!defined('PSR_ERROR_CRITICAL')) define('PSR_ERROR_CRITICAL', PSR_ERROR . '/critical');
        if (!defined('PSR_ERROR_ERROR')) define('PSR_ERROR_ERROR', PSR_ERROR . '/error');

        if (!defined('PSR_DEBUG_DEBUG')) define('PSR_DEBUG_DEBUG', PSR_DEBUG . '/debug');
        if (!defined('PSR_DEBUG_INFO')) define('PSR_DEBUG_INFO', PSR_DEBUG . '/info');

        if (!defined('PSR_ACCESS_WARNING')) define('PSR_ACCESS_WARNING', PSR_ACCESS . '/warning');
        if (!defined('PSR_ACCESS_INFO')) define('PSR_ACCESS_INFO', PSR_ACCESS . '/info');

        if (!defined('PSR_QUERY_ERROR')) define('PSR_QUERY_ERROR', PSR_QUERY . '/error');
        if (!defined('PSR_QUERY_DEBUG')) define('PSR_QUERY_DEBUG', PSR_QUERY . '/debug');

        if (!defined('PSR_AUTH_ERROR')) define('PSR_AUTH_ERROR', PSR_AUTH . '/error');
        if (!defined('PSR_AUTH_WARNING')) define('PSR_AUTH_WARNING', PSR_AUTH . '/warning');
        if (!defined('PSR_AUTH_INFO')) define('PSR_AUTH_INFO', PSR_AUTH . '/info');

        // System PHP logs
        if (!defined('SYSTEM_PHP_LOGS_PATH')) define('SYSTEM_PHP_LOGS_PATH', SYSTEM_LOGS_PATH . '/php');
        if (!defined('SYSTEM_PHP_ERROR')) define('SYSTEM_PHP_ERROR', SYSTEM_PHP_LOGS_PATH . '/error');
        if (!defined('SYSTEM_PHP_WARNING')) define('SYSTEM_PHP_WARNING', SYSTEM_PHP_LOGS_PATH . '/warning');
        if (!defined('SYSTEM_PHP_NOTICE')) define('SYSTEM_PHP_NOTICE', SYSTEM_PHP_LOGS_PATH . '/notice');
        if (!defined('SYSTEM_PHP_DEBUG')) define('SYSTEM_PHP_DEBUG', SYSTEM_PHP_LOGS_PATH . '/debug');

        // Log categories
        if (!defined('LOG_SYSTEM')) define('LOG_SYSTEM', SYSTEM_LOGS_PATH . '/system');
        if (!defined('LOG_ERROR')) define('LOG_ERROR', SYSTEM_LOGS_PATH . '/error');
        if (!defined('LOG_ACCESS')) define('LOG_ACCESS', SYSTEM_LOGS_PATH . '/access');
        if (!defined('LOG_QUERY')) define('LOG_QUERY', SYSTEM_LOGS_PATH . '/query');
        if (!defined('LOG_DEBUG')) define('LOG_DEBUG', SYSTEM_LOGS_PATH . '/debug');

        // Log file extensions
        if (!defined('LOG_ERROR_FILE')) define('LOG_ERROR_FILE', '/error.log');
        if (!defined('LOG_WARNING_FILE')) define('LOG_WARNING_FILE', '/warning.log');
        if (!defined('LOG_NOTICE_FILE')) define('LOG_NOTICE_FILE', '/notice.log');
        if (!defined('LOG_INFO_FILE')) define('LOG_INFO_FILE', '/info.log');
        if (!defined('LOG_DEBUG_FILE')) define('LOG_DEBUG_FILE', '/debug.log');

        // PHP log files
        if (!defined('PHP_ERROR_LOG')) define('PHP_ERROR_LOG', LOG_ERROR . LOG_ERROR_FILE);
        if (!defined('PHP_WARNING_LOG')) define('PHP_WARNING_LOG', LOG_ERROR . LOG_WARNING_FILE);
        if (!defined('PHP_NOTICE_LOG')) define('PHP_NOTICE_LOG', LOG_ERROR . LOG_NOTICE_FILE);
        if (!defined('PHP_DEBUG_LOG')) define('PHP_DEBUG_LOG', LOG_ERROR . LOG_DEBUG_FILE);



        // Application Configuration
        if (!defined('APP_NAME')) define('APP_NAME', $_ENV['APP_NAME'] ?? 'Bimbel Online');
        if (!defined('APP_VERSION')) define('APP_VERSION', $_ENV['APP_VERSION'] ?? '1.0.0');
        if (!defined('APP_ENV')) define('APP_ENV', $_ENV['APP_ENV'] ?? 'development');
        if (!defined('APP_DEBUG')) define('APP_DEBUG', $_ENV['APP_DEBUG'] ?? true);

        // Detect the app URL automatically if not set in .env
        if (!defined('APP_URL')) {
            // Check if we're in a CLI environment
            if (PHP_SAPI === 'cli') {
                define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost');
            } else {
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
                define('APP_URL', $_ENV['APP_URL'] ?? "{$protocol}{$host}");
            }
        }

        if (!defined('APP_TIMEZONE')) define('APP_TIMEZONE', $_ENV['APP_TIMEZONE'] ?? 'Asia/Jakarta');
        if (!defined('APP_CHARSET')) define('APP_CHARSET', $_ENV['APP_CHARSET'] ?? 'UTF-8');

        // URL Configuration
        // Detect the base URL and path automatically if not set in .env
        if (!defined('BASE_URL')) {
            // Check if we're in a CLI environment
            if (PHP_SAPI === 'cli') {
                define('BASE_URL', $_ENV['BASE_URL'] ?? 'http://localhost');
            } else {
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
                define('BASE_URL', $_ENV['BASE_URL'] ?? "{$protocol}{$host}");
            }
        }

        // Detect the base path from the script name
        if (!defined('BASE_PATH')) {
            // Check if we're in a CLI environment
            if (PHP_SAPI === 'cli') {
                define('BASE_PATH', $_ENV['BASE_PATH'] ?? '');
            } else {
                // Get the script name and directory
                $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
                $scriptDir = dirname($scriptName);

                // Handle different server configurations
                // If we're in the public directory, extract the base path, otherwise use the script directory as is
                $basePath = strpos($scriptDir, '/public') !== false ? str_replace('/public', '', $scriptDir) : $scriptDir;

                // Special case for root directory
                if ($basePath === '/' || $basePath === '\\') {
                    $basePath = '';
                }

                // Remove trailing slash if it exists
                $basePath = rtrim($basePath, '/');

                define('BASE_PATH', $_ENV['BASE_PATH'] ?? $basePath);
            }
        }

        // Define URLs for public and assets directories based on environment
        if (!defined('PUBLIC_URL')) {
            // PENGATURAN URL RELATIF VS ABSOLUT
            // Jika USE_RELATIVE_URLS = true, maka gunakan URL relatif tanpa domain
            // Jika USE_RELATIVE_URLS = false, maka gunakan URL absolut dengan domain
            if (defined('USE_RELATIVE_URLS') && USE_RELATIVE_URLS) {
                // Use relative URLs without domain for local development
                if (BASE_PATH === '') {
                    // Application is in the document root
                    define('PUBLIC_URL', '');
                } else {
                    // Application is in a subdirectory
                    define('PUBLIC_URL', BASE_PATH);
                }
            } else {
                // Use absolute URLs with domain
                switch (ENV_TYPE) {
                    case 'hosting':
                        // In hosting environment, public directory is usually the document root
                        define('PUBLIC_URL', BASE_URL);
                        break;

                    case 'virtualhost':
                        // In virtual host, public directory might be the document root
                        define('PUBLIC_URL', BASE_URL);
                        break;

                    case 'local':
                    case 'cli':
                    default:
                        // For local development or CLI, check if in document root or subdirectory
                        if (BASE_PATH === '') {
                            // Application is in the document root
                            define('PUBLIC_URL', BASE_URL);
                        } else {
                            // Application is in a subdirectory
                            define('PUBLIC_URL', BASE_URL . BASE_PATH);
                        }
                        break;
                }
            }
        }

        // Define assets URL based on the structure in tree.txt and environment
        if (!defined('ASSETS_URL')) {
            // Assets are directly in the public directory in all environments
            // PENGATURAN URL RELATIF VS ABSOLUT UNTUK ASSETS
            // Jika USE_RELATIVE_URLS = true, maka gunakan URL relatif tanpa domain
            // Jika USE_RELATIVE_URLS = false, maka gunakan URL absolut dengan domain
            if (defined('USE_RELATIVE_URLS') && USE_RELATIVE_URLS) {
                // For relative URLs, we need to ensure the path is correct
                if (PUBLIC_URL === '') {
                    define('ASSETS_URL', '');
                } else {
                    define('ASSETS_URL', PUBLIC_URL);
                }
            } else {
                // For absolute URLs, just use PUBLIC_URL
                define('ASSETS_URL', PUBLIC_URL);
            }
        }

        // Define physical paths
        if (!defined('PUBLIC_PATH')) {
            define('PUBLIC_PATH', ROOT_PATH . '/public');
        }

        // For backward compatibility and physical file operations
        if (!defined('ASSETS_PATH')) {
            define('ASSETS_PATH', PUBLIC_PATH);
        }

        // Debug & Logging Configuration
        if (!defined('DEBUG_MODE')) define('DEBUG_MODE', APP_DEBUG);
        if (!defined('LOGGING_MODE')) define('LOGGING_MODE', $_ENV['LOGGING_MODE'] ?? true);
        if (!defined('USE_DATABASE')) define('USE_DATABASE', $_ENV['USE_DATABASE'] ?? true);
        if (!defined('LOG_BACKUP_MODE')) define('LOG_BACKUP_MODE', $_ENV['LOG_BACKUP_MODE'] ?? false);
        if (!defined('ERROR_REPORTING')) define('ERROR_REPORTING', APP_DEBUG ? E_ALL : E_ALL & ~E_DEPRECATED & ~E_STRICT);
        if (!defined('DISPLAY_ERRORS')) define('DISPLAY_ERRORS', APP_DEBUG ? '1' : '0');
        if (!defined('LOG_ERRORS')) define('LOG_ERRORS', '1');

        // Log File Configuration
        define('LOG_MAX_SIZE', $_ENV['LOG_MAX_SIZE'] ?? 10 * 1024 * 1024);
        define('LOG_BACKUP_EXT', '.bak');
        define('LOG_BACKUP_FORMAT', 'Y-m-d_H-i-s');
        define('LOG_BACKUP_PATH', LOGS_PATH . '/backups');
        define('LOG_MAX_BACKUPS', $_ENV['LOG_MAX_BACKUPS'] ?? 5);
        define('LOG_STATIC_FILES', $_ENV['LOG_STATIC_FILES'] ?? false);

        // Apply error reporting settings
        self::initErrorHandling();

        // Set timezone
        date_default_timezone_set(APP_TIMEZONE);

        // Ensure required directories exist
        self::ensureRequiredDirectories();

        // Informasi konfigurasi URL dalam komentar HTML (untuk debugging)
        echo "<!-- URL Configuration: " . (USE_RELATIVE_URLS ? 'Using Relative URLs' : 'Using Absolute URLs') . " -->\n";
        echo "<!-- Environment Type: " . ENV_TYPE . " -->\n";
        echo "<!-- BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . " -->\n";
        echo "<!-- BASE_PATH: " . (defined('BASE_PATH') ? BASE_PATH : 'Not defined') . " -->\n";
    }

    /**
     * Detect the current environment (local, hosting, virtual host)
     * and set appropriate constants
     *
     * @return void
     */
    private static function detectEnvironment(): void
    {
        // Default to development environment
        if (!defined('APP_ENV')) {
            define('APP_ENV', $_ENV['APP_ENV'] ?? 'development');
        }

        // Check if we're in a CLI environment
        if (PHP_SAPI === 'cli') {
            if (!defined('ENV_TYPE')) define('ENV_TYPE', 'cli');
            return;
        }

        // Detect environment based on server name
        $serverName = $_SERVER['SERVER_NAME'] ?? '';

        // Local environment detection
        if (strpos($serverName, 'localhost') !== false ||
            strpos($serverName, '127.0.0.1') !== false ||
            strpos($serverName, '.local') !== false ||
            strpos($serverName, '.test') !== false ||
            strpos($serverName, '_') === 0) { // Deteksi nama domain yang dimulai dengan underscore
            if (!defined('ENV_TYPE')) define('ENV_TYPE', 'local');
            if (!defined('IS_LOCAL_DOMAIN')) define('IS_LOCAL_DOMAIN', true);

            // UBAH DISINI UNTUK LINGKUNGAN LOCAL (localhost)
            // true = URL relatif (/myapp/page), false = URL absolut (http://localhost/myapp/page)
            if (!defined('USE_RELATIVE_URLS')) {
                define('USE_RELATIVE_URLS', false); // Menggunakan URL absolut untuk lingkungan lokal
            }
        }
        // Virtual host detection
        else if (strpos($serverName, '.') === false) {
            if (!defined('ENV_TYPE')) define('ENV_TYPE', 'virtualhost');
            if (!defined('IS_LOCAL_DOMAIN')) define('IS_LOCAL_DOMAIN', true);

            // UBAH DISINI UNTUK LINGKUNGAN VIRTUAL HOST (myapp.test)
            // true = URL relatif (/page), false = URL absolut (http://myapp.test/page)
            if (!defined('USE_RELATIVE_URLS')) {
                define('USE_RELATIVE_URLS', false); // Menggunakan URL absolut untuk virtual host
            }
        }
        // Hosting/production detection
        else {
            if (!defined('ENV_TYPE')) define('ENV_TYPE', 'hosting');
            if (!defined('IS_LOCAL_DOMAIN')) define('IS_LOCAL_DOMAIN', false);

            // UBAH DISINI UNTUK LINGKUNGAN HOSTING/PRODUKSI (example.com)
            // true = URL relatif (/page), false = URL absolut (http://example.com/page)
            if (!defined('USE_RELATIVE_URLS')) {
                define('USE_RELATIVE_URLS', false); // Menggunakan URL absolut untuk lingkungan produksi
            }
        }
    }

    /**
     * Ensure required directories exist
     *
     * @return void
     */
    private static function ensureRequiredDirectories(): void
    {
        // Ensure storage directory exists
        if (!is_dir(STORAGE_PATH)) {
            mkdir(STORAGE_PATH, 0777, true);
        }

        // Ensure public/assets directory exists
        if (!is_dir(ASSETS_PATH)) {
            mkdir(ASSETS_PATH, 0777, true);
        }

        // Create subdirectories in assets if they don't exist
        $assetSubdirs = ['css', 'js', 'img', 'fonts'];
        foreach ($assetSubdirs as $dir) {
            $path = ASSETS_PATH . '/' . $dir;
            if (!is_dir($path)) {
                mkdir($path, 0777, true);
            }
        }
    }

    /**
     * Get URL configuration information
     *
     * @return array Array containing URL configuration information
     */
    public static function getUrlConfig(): array
    {
        return [
            'USE_RELATIVE_URLS' => defined('USE_RELATIVE_URLS') ? USE_RELATIVE_URLS : false,
            'BASE_URL' => defined('BASE_URL') ? BASE_URL : '',
            'BASE_PATH' => defined('BASE_PATH') ? BASE_PATH : '',
            'PUBLIC_URL' => defined('PUBLIC_URL') ? PUBLIC_URL : '',
            'ASSETS_URL' => defined('ASSETS_URL') ? ASSETS_URL : '',
            'ENV_TYPE' => defined('ENV_TYPE') ? ENV_TYPE : 'unknown',
            'IS_LOCAL_DOMAIN' => defined('IS_LOCAL_DOMAIN') ? IS_LOCAL_DOMAIN : false
        ];
    }

    /**
     * Create required log directories
     *
     * @return void
     */
    private static function createLogDirectories(): void
    {
        $directories = [
            LOGS_PATH,
            PSR_LOGS_PATH,
            SYSTEM_LOGS_PATH,
            LOG_SYSTEM,
            LOG_ERROR,
            LOG_ACCESS,
            LOG_QUERY,
            LOG_DEBUG,
            LOG_BACKUP_PATH,
            // PSR log categories
            PSR_SYSTEM,
            PSR_ERROR,
            PSR_DEBUG,
            PSR_ACCESS,
            PSR_QUERY,
            PSR_AUTH,
            // PSR log subcategories
            PSR_SYSTEM_ERROR,
            PSR_SYSTEM_DEBUG,
            PSR_SYSTEM_INFO,
            PSR_ERROR_EMERGENCY,
            PSR_ERROR_CRITICAL,
            PSR_ERROR_ERROR,
            PSR_DEBUG_DEBUG,
            PSR_DEBUG_INFO,
            PSR_ACCESS_WARNING,
            PSR_ACCESS_INFO,
            PSR_QUERY_ERROR,
            PSR_QUERY_DEBUG,
            PSR_AUTH_ERROR,
            PSR_AUTH_WARNING,
            PSR_AUTH_INFO,
            // System PHP logs
            SYSTEM_PHP_LOGS_PATH,
            SYSTEM_PHP_ERROR,
            SYSTEM_PHP_WARNING,
            SYSTEM_PHP_NOTICE,
            SYSTEM_PHP_DEBUG
        ];

        foreach ($directories as $dir) {
            // Ensure directory is a string and not empty
            if (is_string($dir) && !empty($dir) && !is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
        }
    }

    /**
     * Initialize error handling settings
     *
     * @return void
     */
    private static function initErrorHandling(): void
    {
        error_reporting(ERROR_REPORTING);
        ini_set('display_errors', DISPLAY_ERRORS);
        ini_set('log_errors', LOG_ERRORS);
        ini_set('error_log', PHP_ERROR_LOG);
    }
}


