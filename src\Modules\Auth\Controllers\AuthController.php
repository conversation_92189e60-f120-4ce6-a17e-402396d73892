<?php

declare(strict_types=1);

namespace Modules\Auth\Controllers;

use Contracts\Logger\LoggerInterface;
use Contracts\Toast\ToastInterface;
use Core\Controller;
use Modules\Auth\Models\AuthModel;
use Providers\Logger\LoggerProvider;
use Providers\Toast\ToastProvider;

/**
 * Auth Controller
 *
 * Handles authentication, login, logout, and user profile management
 */
class AuthController extends Controller
{
    /**
     * Auth model instance
     */
    private AuthModel $authModel;

    /**
     * Toast service instance
     */
    private ToastInterface $toast;

    /**
     * Logger instance
     */
    private LoggerInterface $authLogger;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();

        // Ensure session is started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Initialize dependencies using dependency injection
        $this->authModel = new AuthModel();
        $this->toast = ToastProvider::getToast();
        $this->authLogger = LoggerProvider::getLogger(LoggerProvider::CATEGORY_AUTH);
    }

    /**
     * Default action
     */
    public function index(): void
    {
        $this->login();
    }

    /**
     * Display login page
     */
    public function login(): void
    {
        // Redirect to dashboard if already logged in
        if (isset($_SESSION['user_id'])) {
            $this->authLogger->debug("Already logged in, redirecting to dashboard", [
                'user_id' => $_SESSION['user_id']
            ]);
            header('Location: ' . APP_URL . '/dashboard');
            exit;
        }

        $data = [
            'title' => 'Login - ' . APP_NAME,
            'content' => ROOT_PATH . '/src/Modules/Auth/Views/partials/login.php'
        ];

        $this->view->render('Auth/Views/layouts/auth', $data);
    }

    /**
     * Handle authentication
     */
    public function authenticate(): void
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->toast->error('Invalid request method');
                $this->authLogger->warning('Invalid request method for authentication');
                header('Location: ' . APP_URL . '/login');
                exit;
            }

            $username = trim($_POST['username'] ?? '');
            $password = trim($_POST['password'] ?? '');

            if (empty($username) || empty($password)) {
                $this->toast->error('Username dan password harus diisi');
                $_SESSION['old_username'] = $username;
                $this->authLogger->warning('Empty username or password', ['username' => $username]);
                header('Location: ' . APP_URL . '/login');
                exit;
            }

            $user = $this->authModel->authenticate($username, $password);

            if (!$user) {
                $this->toast->error('Username atau password salah');
                $_SESSION['old_username'] = $username;
                $this->authLogger->warning('Invalid credentials', ['username' => $username]);
                header('Location: ' . APP_URL . '/login');
                exit;
            }

            // Set session variables with role information
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role_id'] = $user['role_id'];
            $_SESSION['role_name'] = $user['role_name'];
            $_SESSION['permissions'] = $user['permissions'];
            $_SESSION['last_login'] = $user['last_login'];

            // Get extended profile based on role
            $extendedProfile = $this->authModel->getUserExtendedProfile($user['id'], $user['role_name']);
            if ($extendedProfile) {
                $_SESSION['profile'] = $extendedProfile;
            }

            $this->toast->success('Login berhasil! Selamat datang, ' . ($extendedProfile['nama_lengkap'] ?? $user['username']));

            // Redirect to role-specific dashboard
            $dashboardUrl = $this->authModel->getDashboardUrl($user['role_name']);
            header('Location: ' . APP_URL . $dashboardUrl);
            exit;

        } catch (\Throwable $e) {
            $this->toast->error($e->getMessage());
            $_SESSION['old_username'] = $username ?? '';
            $this->authLogger->error('Authentication error', [
                'username' => $username ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            header('Location: ' . APP_URL . '/login');
            exit;
        }
    }

    /**
     * Handle logout
     */
    public function logout(): void
    {
        if (isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
            $this->authLogger->info('User logging out', ['user_id' => $userId]);

            // Log activity before destroying session
            $this->authModel->logActivity(
                $userId,
                'logout',
                'pengguna',
                $userId,
                'User logged out successfully'
            );

            // Clear all session data
            session_unset();
            session_destroy();

            $this->toast->success('Anda telah berhasil logout');
        }

        header('Location: ' . APP_URL . '/login');
        exit;
    }

    /**
     * Display user profile
     */
    public function profile(): void
    {
        if (!isset($_SESSION['user_id'])) {
            $this->authLogger->warning('Unauthorized access to profile');
            header('Location: ' . APP_URL . '/login');
            exit;
        }

        $userId = $_SESSION['user_id'];
        $user = $this->authModel->getUserById($userId);

        if (!$user) {
            $this->toast->error('User tidak ditemukan');
            $this->authLogger->error('User not found', ['user_id' => $userId]);
            header('Location: ' . APP_URL);
            exit;
        }

        $data = [
            'title' => 'Profile - ' . APP_NAME,
            'user' => $user,
            'content' => ROOT_PATH . '/src/Modules/Auth/Views/partials/profile.php'
        ];

        $this->authLogger->debug('Profile viewed', ['user_id' => $userId]);
        $this->view->render('Auth/Views/layouts/auth', $data);
    }

    /**
     * Handle password change
     */
    public function changePassword(): void
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new \RuntimeException('Invalid request method');
            }

            if (!isset($_SESSION['user_id'])) {
                $this->authLogger->warning('Unauthorized password change attempt');
                throw new \RuntimeException('Unauthorized access');
            }

            $userId = $_SESSION['user_id'];
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                $this->authLogger->warning('Empty password fields', ['user_id' => $userId]);
                throw new \RuntimeException('Semua field harus diisi');
            }

            if ($newPassword !== $confirmPassword) {
                $this->authLogger->warning('Password mismatch', ['user_id' => $userId]);
                throw new \RuntimeException('Password baru tidak cocok');
            }

            if (!$this->authModel->verifyCurrentPassword($userId, $currentPassword)) {
                $this->authLogger->warning('Invalid current password', ['user_id' => $userId]);
                throw new \RuntimeException('Password saat ini salah');
            }

            $this->authModel->updatePassword($userId, $newPassword);

            // Log activity
            $this->authModel->logActivity(
                $userId,
                'update',
                'pengguna',
                $userId,
                'User changed password'
            );

            $this->toast->success('Password berhasil diubah');
            $this->authLogger->info('Password changed successfully', ['user_id' => $userId]);
            header('Location: ' . APP_URL . '/auth/profile');
            exit;

        } catch (\Throwable $e) {
            $this->toast->error($e->getMessage());
            $this->authLogger->error('Password change error', [
                'user_id' => $_SESSION['user_id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            header('Location: ' . APP_URL . '/auth/profile');
            exit;
        }
    }
}









