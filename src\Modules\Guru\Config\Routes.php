<?php

declare(strict_types=1);

/**
 * Guru <PERSON>le Routes Configuration
 *
 * Defines routes for the Guru module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\Guru\Config
 */

return [
    'name' => 'Guru',
    'namespace' => '<PERSON><PERSON><PERSON>\\Guru',
    'default_controller' => 'GuruController',
    'default_action' => 'dashboard',

    'routes' => [
        'guru' => [
            'controller' => 'GuruController',
            'action' => 'dashboard'
        ],
        'guru/dashboard' => [
            'controller' => 'GuruController',
            'action' => 'dashboard'
        ],
        'guru/profile' => [
            'controller' => 'GuruController',
            'action' => 'profile'
        ],
        'guru/profile/edit' => [
            'controller' => 'GuruController',
            'action' => 'editProfile'
        ],
        'guru/profile/update' => [
            'controller' => 'GuruController',
            'action' => 'updateProfile'
        ],
        'guru/kelas' => [
            'controller' => 'KelasController',
            'action' => 'index'
        ],
        'guru/kelas/{id}' => [
            'controller' => 'KelasController',
            'action' => 'detail'
        ],
        'guru/kelas/{id}/siswa' => [
            'controller' => 'KelasController',
            'action' => 'daftarSiswa'
        ],
        'guru/rencana-pengajaran' => [
            'controller' => 'RencanaPengajaranController',
            'action' => 'index'
        ],
        'guru/rencana-pengajaran/create' => [
            'controller' => 'RencanaPengajaranController',
            'action' => 'create'
        ],
        'guru/rencana-pengajaran/{id}' => [
            'controller' => 'RencanaPengajaranController',
            'action' => 'detail'
        ],
        'guru/rencana-pengajaran/{id}/edit' => [
            'controller' => 'RencanaPengajaranController',
            'action' => 'edit'
        ],
        'guru/rencana-pengajaran/{id}/update' => [
            'controller' => 'RencanaPengajaranController',
            'action' => 'update'
        ],
        'guru/presensi' => [
            'controller' => 'PresensiController',
            'action' => 'index'
        ],
        'guru/presensi/create' => [
            'controller' => 'PresensiController',
            'action' => 'create'
        ],
        'guru/presensi/{kelas_id}/{tanggal}' => [
            'controller' => 'PresensiController',
            'action' => 'detail'
        ],
        'guru/presensi/save' => [
            'controller' => 'PresensiController',
            'action' => 'save'
        ],
        'guru/nilai' => [
            'controller' => 'NilaiController',
            'action' => 'index'
        ],
        'guru/nilai/create' => [
            'controller' => 'NilaiController',
            'action' => 'create'
        ],
        'guru/nilai/{id}/edit' => [
            'controller' => 'NilaiController',
            'action' => 'edit'
        ],
        'guru/nilai/save' => [
            'controller' => 'NilaiController',
            'action' => 'save'
        ],
        'guru/laporan' => [
            'controller' => 'LaporanController',
            'action' => 'index'
        ],
        'guru/laporan/create' => [
            'controller' => 'LaporanController',
            'action' => 'create'
        ],
        'guru/laporan/{id}' => [
            'controller' => 'LaporanController',
            'action' => 'detail'
        ],
        'guru/laporan/{id}/edit' => [
            'controller' => 'LaporanController',
            'action' => 'edit'
        ],
        'guru/laporan/generate' => [
            'controller' => 'LaporanController',
            'action' => 'generate'
        ],
        'guru/diskusi' => [
            'controller' => 'DiskusiController',
            'action' => 'index'
        ],
        'guru/diskusi/create' => [
            'controller' => 'DiskusiController',
            'action' => 'create'
        ],
        'guru/diskusi/{id}' => [
            'controller' => 'DiskusiController',
            'action' => 'view'
        ],
        'guru/diskusi/{id}/reply' => [
            'controller' => 'DiskusiController',
            'action' => 'reply'
        ]
    ]
];
