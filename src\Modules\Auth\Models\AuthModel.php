<?php

declare(strict_types=1);

namespace Modules\Auth\Models;

use Contracts\Logger\LoggerInterface;
use Core\Model;
use Providers\Logger\LoggerProvider;

/**
 * Auth Model
 *
 * Handles authentication and user management
 */
class AuthModel extends Model
{
    /**
     * Logger instance
     */
    private LoggerInterface $authLogger;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->authLogger = LoggerProvider::getLogger(LoggerProvider::CATEGORY_AUTH);
    }

    /**
     * Authenticate a user
     *
     * @param string $username The username
     * @param string $password The password
     * @return array|null The user data if authenticated, null otherwise
     */
    public function authenticate(string $username, string $password): ?array
    {
        try {
            $sql = "SELECT u.*, r.nama as role_name, r.permissions
                    FROM users u
                    JOIN roles r ON u.role_id = r.id
                    WHERE u.username = ? OR u.email = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                $this->authLogger->info("Failed login attempt", ['username' => $username]);
                return null;
            }

            if (!$user['status_aktif']) {
                $this->authLogger->info("Inactive user login attempt", ['username' => $username, 'user_id' => $user['id']]);
                return null;
            }

            // Update last login
            $this->updateLastLogin($user['id']);

            $this->authLogger->info("Successful login", ['user_id' => $user['id'], 'username' => $username, 'role' => $user['role_name']]);
            return $user;
        } catch (\PDOException $e) {
            $this->authLogger->error("Database error during authentication", ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Update last login timestamp
     *
     * @param int $userId The user ID
     * @return void
     */
    private function updateLastLogin(int $userId): void
    {
        try {
            $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
        } catch (\PDOException $e) {
            $this->authLogger->warning("Failed to update last login", ['user_id' => $userId, 'error' => $e->getMessage()]);
        }
    }

    /**
     * Get user profile with role information
     *
     * @param int $userId The user ID
     * @return array|null The user profile data
     */
    public function getUserProfile(int $userId): ?array
    {
        try {
            $sql = "SELECT u.*, r.nama as role_name, r.permissions
                    FROM users u
                    JOIN roles r ON u.role_id = r.id
                    WHERE u.id = ? AND u.status_aktif = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (\PDOException $e) {
            $this->authLogger->error("Error fetching user profile", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get user extended profile based on role
     *
     * @param int $userId The user ID
     * @param string $role The user role
     * @return array|null The extended profile data
     */
    public function getUserExtendedProfile(int $userId, string $role): ?array
    {
        try {
            $baseProfile = $this->getUserProfile($userId);
            if (!$baseProfile) {
                return null;
            }

            $extendedData = [];

            switch ($role) {
                case 'guru':
                    $sql = "SELECT g.*, GROUP_CONCAT(mp.nama) as mata_pelajaran
                            FROM guru g
                            LEFT JOIN guru_mata_pelajaran gmp ON g.id = gmp.guru_id
                            LEFT JOIN mata_pelajaran mp ON gmp.mata_pelajaran_id = mp.id
                            WHERE g.user_id = ?
                            GROUP BY g.id";
                    break;

                case 'siswa':
                    $sql = "SELECT s.*, ot.nama_lengkap as nama_orang_tua, ot.no_telepon as telepon_orang_tua
                            FROM siswa s
                            JOIN orang_tua ot ON s.orang_tua_id = ot.id
                            WHERE s.user_id = ?";
                    break;

                case 'orang_tua':
                    $sql = "SELECT ot.*, GROUP_CONCAT(s.nama_lengkap) as nama_anak
                            FROM orang_tua ot
                            LEFT JOIN siswa s ON ot.id = s.orang_tua_id
                            WHERE ot.user_id = ?
                            GROUP BY ot.id";
                    break;

                default:
                    return $baseProfile;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $extendedData = $stmt->fetch();

            return array_merge($baseProfile, $extendedData ?: []);
        } catch (\PDOException $e) {
            $this->authLogger->error("Error fetching extended profile", ['user_id' => $userId, 'role' => $role, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Check if user has permission
     *
     * @param int $userId The user ID
     * @param string $permission The permission to check
     * @return bool True if user has permission
     */
    public function hasPermission(int $userId, string $permission): bool
    {
        try {
            $user = $this->getUserProfile($userId);
            if (!$user) {
                return false;
            }

            $permissions = json_decode($user['permissions'], true);

            // Admin has all permissions
            if (in_array('all', $permissions)) {
                return true;
            }

            return in_array($permission, $permissions);
        } catch (\Exception $e) {
            $this->authLogger->error("Error checking permission", ['user_id' => $userId, 'permission' => $permission, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get user dashboard URL based on role
     *
     * @param string $role The user role
     * @return string The dashboard URL
     */
    public function getDashboardUrl(string $role): string
    {
        switch ($role) {
            case 'admin':
                return '/admin/dashboard';
            case 'guru':
                return '/guru/dashboard';
            case 'siswa':
                return '/siswa/dashboard';
            case 'orang_tua':
                return '/orang-tua/dashboard';
            default:
                return '/dashboard';
        }
    }

    /**
     * Get user by ID with role information
     *
     * @param int $userId The user ID
     * @return array|null The user data or null if not found
     */
    public function getUserById(int $userId): ?array
    {
        try {
            $sql = "SELECT u.*, r.nama as role_name, r.permissions
                    FROM users u
                    JOIN roles r ON u.role_id = r.id
                    WHERE u.id = ? AND u.status_aktif = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $result = $stmt->fetch() ?: null;

            if ($result === null) {
                $this->authLogger->debug("User not found", ['user_id' => $userId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->authLogger->error("Error fetching user by ID", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Verify the current password for a user
     *
     * @param int $userId The user ID
     * @param string $password The password to verify
     * @return bool True if the password is correct
     */
    public function verifyCurrentPassword(int $userId, string $password): bool
    {
        try {
            $sql = "SELECT password FROM users WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            $result = $user && password_verify($password, $user['password']);
            $this->authLogger->debug("Password verification", [
                'user_id' => $userId,
                'result' => $result ? 'success' : 'failure'
            ]);

            return $result;
        } catch (\PDOException $e) {
            $this->authLogger->error("Error verifying password", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Update the password for a user
     *
     * @param int $userId The user ID
     * @param string $newPassword The new password
     * @return bool True if password was updated successfully
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$hashedPassword, $userId]);

            if ($result) {
                $this->authLogger->info("Password updated", ['user_id' => $userId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->authLogger->error("Error updating password", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Create new user account
     *
     * @param array $userData User data
     * @return int|null The new user ID or null if failed
     */
    public function createUser(array $userData): ?int
    {
        try {
            $sql = "INSERT INTO users (username, email, password, role_id, status_aktif)
                    VALUES (?, ?, ?, ?, ?)";

            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $userData['username'],
                $userData['email'],
                $hashedPassword,
                $userData['role_id'],
                $userData['status_aktif'] ?? true
            ]);

            if ($result) {
                $userId = (int)$this->db->lastInsertId();
                $this->authLogger->info("User created", ['user_id' => $userId, 'username' => $userData['username']]);
                return $userId;
            }

            return null;
        } catch (\PDOException $e) {
            $this->authLogger->error("Error creating user", ['error' => $e->getMessage(), 'username' => $userData['username'] ?? 'unknown']);
            return null;
        }
    }
}

