<?php

declare(strict_types=1);

/**
 * Orang Tua Module Routes Configuration
 *
 * Defines routes for the Orang Tua module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\OrangTua\Config
 */

return [
    'name' => 'OrangTua',
    'namespace' => 'Modules\\OrangTua',
    'default_controller' => 'OrangTuaController',
    'default_action' => 'dashboard',

    'routes' => [
        'orang-tua' => [
            'controller' => 'OrangTuaController',
            'action' => 'dashboard'
        ],
        'orang-tua/dashboard' => [
            'controller' => 'OrangTuaController',
            'action' => 'dashboard'
        ],
        'orang-tua/profile' => [
            'controller' => 'OrangTuaController',
            'action' => 'profile'
        ],
        'orang-tua/profile/edit' => [
            'controller' => 'OrangTuaController',
            'action' => 'editProfile'
        ],
        'orang-tua/profile/update' => [
            'controller' => 'OrangTuaController',
            'action' => 'updateProfile'
        ],
        'orang-tua/anak' => [
            'controller' => 'OrangTuaController',
            'action' => 'daftarAnak'
        ],
        'orang-tua/anak/{id}' => [
            'controller' => 'OrangTuaController',
            'action' => 'detailAnak'
        ],
        'orang-tua/laporan' => [
            'controller' => 'LaporanController',
            'action' => 'index'
        ],
        'orang-tua/laporan/{id}' => [
            'controller' => 'LaporanController',
            'action' => 'detail'
        ],
        'orang-tua/laporan/download/{id}' => [
            'controller' => 'LaporanController',
            'action' => 'download'
        ],
        'orang-tua/pembayaran' => [
            'controller' => 'PembayaranController',
            'action' => 'index'
        ],
        'orang-tua/pembayaran/{id}' => [
            'controller' => 'PembayaranController',
            'action' => 'detail'
        ],
        'orang-tua/pembayaran/{id}/bayar' => [
            'controller' => 'PembayaranController',
            'action' => 'bayar'
        ],
        'orang-tua/presensi' => [
            'controller' => 'PresensiController',
            'action' => 'index'
        ],
        'orang-tua/presensi/{siswa_id}' => [
            'controller' => 'PresensiController',
            'action' => 'detail'
        ],
        'orang-tua/nilai' => [
            'controller' => 'NilaiController',
            'action' => 'index'
        ],
        'orang-tua/nilai/{siswa_id}' => [
            'controller' => 'NilaiController',
            'action' => 'detail'
        ],
        'orang-tua/diskusi' => [
            'controller' => 'DiskusiController',
            'action' => 'index'
        ],
        'orang-tua/diskusi/create' => [
            'controller' => 'DiskusiController',
            'action' => 'create'
        ],
        'orang-tua/diskusi/{id}' => [
            'controller' => 'DiskusiController',
            'action' => 'view'
        ],
        'orang-tua/diskusi/{id}/reply' => [
            'controller' => 'DiskusiController',
            'action' => 'reply'
        ],
        'orang-tua/notifikasi' => [
            'controller' => 'NotifikasiController',
            'action' => 'index'
        ],
        'orang-tua/notifikasi/{id}/read' => [
            'controller' => 'NotifikasiController',
            'action' => 'markAsRead'
        ]
    ]
];
