<?php

declare(strict_types=1);

/**
 * Diskusi Module Routes Configuration
 *
 * Defines routes for the Diskusi module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\Diskusi\Config
 */

return [
    'name' => 'Diskusi',
    'namespace' => 'Modules\\Diskusi',
    'default_controller' => 'DiskusiController',
    'default_action' => 'index',

    'routes' => [
        'diskusi' => [
            'controller' => 'DiskusiController',
            'action' => 'index'
        ],
        'diskusi/create' => [
            'controller' => 'DiskusiController',
            'action' => 'create'
        ],
        'diskusi/store' => [
            'controller' => 'DiskusiController',
            'action' => 'store'
        ],
        'diskusi/{id}' => [
            'controller' => 'DiskusiController',
            'action' => 'view'
        ],
        'diskusi/{id}/edit' => [
            'controller' => 'DiskusiController',
            'action' => 'edit'
        ],
        'diskusi/{id}/update' => [
            'controller' => 'DiskusiController',
            'action' => 'update'
        ],
        'diskusi/{id}/close' => [
            'controller' => 'DiskusiController',
            'action' => 'close'
        ],
        'diskusi/{id}/reopen' => [
            'controller' => 'DiskusiController',
            'action' => 'reopen'
        ],
        'diskusi/{id}/reply' => [
            'controller' => 'PesanController',
            'action' => 'reply'
        ],
        'diskusi/pesan/{id}/edit' => [
            'controller' => 'PesanController',
            'action' => 'edit'
        ],
        'diskusi/pesan/{id}/update' => [
            'controller' => 'PesanController',
            'action' => 'update'
        ],
        'diskusi/pesan/{id}/delete' => [
            'controller' => 'PesanController',
            'action' => 'delete'
        ],
        'diskusi/pesan/{id}/solution' => [
            'controller' => 'PesanController',
            'action' => 'markAsSolution'
        ],
        'diskusi/kategori/{kategori}' => [
            'controller' => 'DiskusiController',
            'action' => 'byCategory'
        ],
        'diskusi/search' => [
            'controller' => 'DiskusiController',
            'action' => 'search'
        ],
        'diskusi/my-topics' => [
            'controller' => 'DiskusiController',
            'action' => 'myTopics'
        ],
        'diskusi/my-replies' => [
            'controller' => 'DiskusiController',
            'action' => 'myReplies'
        ],
        'bimbingan' => [
            'controller' => 'BimbinganController',
            'action' => 'index'
        ],
        'bimbingan/request' => [
            'controller' => 'BimbinganController',
            'action' => 'request'
        ],
        'bimbingan/{id}' => [
            'controller' => 'BimbinganController',
            'action' => 'detail'
        ],
        'bimbingan/{id}/respond' => [
            'controller' => 'BimbinganController',
            'action' => 'respond'
        ],
        'bimbingan/{id}/complete' => [
            'controller' => 'BimbinganController',
            'action' => 'complete'
        ]
    ]
];
