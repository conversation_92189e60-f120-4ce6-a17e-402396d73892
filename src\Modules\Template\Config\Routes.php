<?php

declare(strict_types=1);

/**
 * Template Module Routes Configuration
 *
 * Defines routes for the Template module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\Template\Config
 */

return [
    'name' => 'Template',
    'namespace' => 'Modules\\Template',
    'default_controller' => 'TemplateController',
    'default_action' => 'index',

    'routes' => [
        'template' => [
            'controller' => 'TemplateController',
            'action' => 'index'
        ],
        'template/create' => [
            'controller' => 'TemplateController',
            'action' => 'create'
        ],
        'template/{id}' => [
            'controller' => 'TemplateController',
            'action' => 'detail'
        ],
        'template/{id}/edit' => [
            'controller' => 'TemplateController',
            'action' => 'edit'
        ],
        'template/{id}/update' => [
            'controller' => 'TemplateController',
            'action' => 'update'
        ],
        'template/{id}/delete' => [
            'controller' => 'TemplateController',
            'action' => 'delete'
        ],
        'template/{id}/preview' => [
            'controller' => 'TemplateController',
            'action' => 'preview'
        ],
        'template/generate' => [
            'controller' => 'GenerateController',
            'action' => 'index'
        ],
        'template/generate/{template_id}' => [
            'controller' => 'GenerateController',
            'action' => 'form'
        ],
        'template/generate/{template_id}/process' => [
            'controller' => 'GenerateController',
            'action' => 'process'
        ],
        'template/dokumen' => [
            'controller' => 'DokumenController',
            'action' => 'index'
        ],
        'template/dokumen/{id}' => [
            'controller' => 'DokumenController',
            'action' => 'detail'
        ],
        'template/dokumen/{id}/download' => [
            'controller' => 'DokumenController',
            'action' => 'download'
        ],
        'template/dokumen/{id}/delete' => [
            'controller' => 'DokumenController',
            'action' => 'delete'
        ],
        'template/absensi' => [
            'controller' => 'AbsensiController',
            'action' => 'index'
        ],
        'template/absensi/generate' => [
            'controller' => 'AbsensiController',
            'action' => 'generate'
        ],
        'template/laporan-nilai' => [
            'controller' => 'LaporanNilaiController',
            'action' => 'index'
        ],
        'template/laporan-nilai/generate' => [
            'controller' => 'LaporanNilaiController',
            'action' => 'generate'
        ],
        'template/data-siswa' => [
            'controller' => 'DataSiswaController',
            'action' => 'index'
        ],
        'template/data-siswa/generate' => [
            'controller' => 'DataSiswaController',
            'action' => 'generate'
        ]
    ]
];
