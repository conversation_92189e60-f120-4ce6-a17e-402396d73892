<?php

declare(strict_types=1);

namespace Modules\Diskusi\Models;

use Core\Model;
use PDO;

/**
 * Diskusi Model
 *
 * Handles data operations for discussion forum management
 *
 * @package Modules\Diskusi\Models
 */
class DiskusiModel extends Model
{
    /**
     * Get forum discussions with filters
     *
     * @param array $filters Filters (kategori, status, user_id, search)
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array List of discussions
     */
    public function getDiskusi(array $filters = [], int $limit = 20, int $offset = 0): array
    {
        try {
            $sql = "SELECT fd.*, u.username as created_by_username,
                           COUNT(pf.id) as total_replies,
                           MAX(pf.created_at) as last_reply_at,
                           u_last.username as last_reply_by
                    FROM forum_diskusi fd
                    JOIN users u ON fd.created_by = u.id
                    LEFT JOIN pesan_forum pf ON fd.id = pf.forum_diskusi_id
                    LEFT JOIN users u_last ON pf.user_id = u_last.id AND pf.created_at = (
                        SELECT MAX(created_at) FROM pesan_forum WHERE forum_diskusi_id = fd.id
                    )
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['kategori'])) {
                $sql .= " AND fd.kategori = ?";
                $params[] = $filters['kategori'];
            }
            
            if (!empty($filters['status'])) {
                $sql .= " AND fd.status = ?";
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['user_id'])) {
                $sql .= " AND fd.created_by = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (fd.judul LIKE ? OR fd.deskripsi LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            $sql .= " GROUP BY fd.id
                      ORDER BY fd.created_at DESC
                      LIMIT ? OFFSET ?";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching discussions", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get discussion by ID with messages
     *
     * @param int $id Discussion ID
     * @return array|null Discussion data with messages
     */
    public function getDiskusiById(int $id): ?array
    {
        try {
            // Get discussion details
            $sql = "SELECT fd.*, u.username as created_by_username, r.nama as created_by_role
                    FROM forum_diskusi fd
                    JOIN users u ON fd.created_by = u.id
                    JOIN roles r ON u.role_id = r.id
                    WHERE fd.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            $discussion = $stmt->fetch();
            
            if (!$discussion) {
                return null;
            }
            
            // Get messages
            $sql = "SELECT pf.*, u.username, r.nama as role_name,
                           CASE 
                               WHEN r.nama = 'guru' THEN g.nama_lengkap
                               WHEN r.nama = 'siswa' THEN s.nama_lengkap
                               WHEN r.nama = 'orang_tua' THEN ot.nama_lengkap
                               ELSE u.username
                           END as display_name
                    FROM pesan_forum pf
                    JOIN users u ON pf.user_id = u.id
                    JOIN roles r ON u.role_id = r.id
                    LEFT JOIN guru g ON u.id = g.user_id
                    LEFT JOIN siswa s ON u.id = s.user_id
                    LEFT JOIN orang_tua ot ON u.id = ot.user_id
                    WHERE pf.forum_diskusi_id = ?
                    ORDER BY pf.created_at ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            $discussion['messages'] = $stmt->fetchAll();
            
            return $discussion;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching discussion", ['id' => $id, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Create new discussion
     *
     * @param array $data Discussion data
     * @return int|null The new discussion ID or null if failed
     */
    public function createDiskusi(array $data): ?int
    {
        try {
            $sql = "INSERT INTO forum_diskusi (
                        judul, kategori, deskripsi, status, created_by
                    ) VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['judul'],
                $data['kategori'],
                $data['deskripsi'] ?? null,
                $data['status'] ?? 'aktif',
                $data['created_by']
            ]);

            if ($result) {
                $id = (int)$this->db->lastInsertId();
                $this->getLogger()->info("Discussion created", ['id' => $id, 'judul' => $data['judul']]);
                return $id;
            }

            return null;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error creating discussion", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Update discussion
     *
     * @param int $id Discussion ID
     * @param array $data Updated data
     * @param int $userId User ID (for permission check)
     * @return bool Success status
     */
    public function updateDiskusi(int $id, array $data, int $userId): bool
    {
        try {
            // Check if user can edit (creator or admin)
            if (!$this->canEditDiskusi($id, $userId)) {
                return false;
            }
            
            $sql = "UPDATE forum_diskusi SET 
                        judul = ?, kategori = ?, deskripsi = ?, updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['judul'],
                $data['kategori'],
                $data['deskripsi'] ?? null,
                $id
            ]);

            if ($result) {
                $this->getLogger()->info("Discussion updated", ['id' => $id, 'user_id' => $userId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error updating discussion", ['id' => $id, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Add message to discussion
     *
     * @param array $data Message data
     * @return int|null The new message ID or null if failed
     */
    public function addMessage(array $data): ?int
    {
        try {
            $sql = "INSERT INTO pesan_forum (
                        forum_diskusi_id, parent_id, user_id, pesan, file_attachment
                    ) VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['forum_diskusi_id'],
                $data['parent_id'] ?? null,
                $data['user_id'],
                $data['pesan'],
                $data['file_attachment'] ?? null
            ]);

            if ($result) {
                $id = (int)$this->db->lastInsertId();
                $this->getLogger()->info("Message added", ['id' => $id, 'forum_id' => $data['forum_diskusi_id']]);
                return $id;
            }

            return null;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error adding message", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Mark message as solution
     *
     * @param int $messageId Message ID
     * @param int $userId User ID (for permission check)
     * @return bool Success status
     */
    public function markAsSolution(int $messageId, int $userId): bool
    {
        try {
            // Get message and discussion info
            $sql = "SELECT pf.*, fd.created_by as discussion_creator
                    FROM pesan_forum pf
                    JOIN forum_diskusi fd ON pf.forum_diskusi_id = fd.id
                    WHERE pf.id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$messageId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                return false;
            }
            
            // Check if user can mark as solution (discussion creator or admin)
            if ($message['discussion_creator'] != $userId && !$this->isAdmin($userId)) {
                return false;
            }
            
            $this->db->beginTransaction();
            
            // Remove solution flag from other messages in the same discussion
            $sql = "UPDATE pesan_forum SET is_solution = 0 WHERE forum_diskusi_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$message['forum_diskusi_id']]);
            
            // Mark this message as solution
            $sql = "UPDATE pesan_forum SET is_solution = 1 WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$messageId]);
            
            $this->db->commit();
            $this->getLogger()->info("Message marked as solution", ['message_id' => $messageId, 'user_id' => $userId]);
            
            return true;
        } catch (\PDOException $e) {
            $this->db->rollBack();
            $this->getLogger()->error("Error marking message as solution", ['message_id' => $messageId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Change discussion status
     *
     * @param int $id Discussion ID
     * @param string $status New status
     * @param int $userId User ID (for permission check)
     * @return bool Success status
     */
    public function changeStatus(int $id, string $status, int $userId): bool
    {
        try {
            // Check if user can change status
            if (!$this->canEditDiskusi($id, $userId)) {
                return false;
            }
            
            $sql = "UPDATE forum_diskusi SET status = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$status, $id]);

            if ($result) {
                $this->getLogger()->info("Discussion status changed", ['id' => $id, 'status' => $status, 'user_id' => $userId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error changing discussion status", ['id' => $id, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get discussion statistics
     *
     * @param array $filters Optional filters
     * @return array Statistics data
     */
    public function getStatistics(array $filters = []): array
    {
        try {
            $stats = [];
            
            // Total discussions by category
            $sql = "SELECT kategori, COUNT(*) as total FROM forum_diskusi";
            $params = [];
            
            if (!empty($filters['user_id'])) {
                $sql .= " WHERE created_by = ?";
                $params[] = $filters['user_id'];
            }
            
            $sql .= " GROUP BY kategori";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['by_category'] = $stmt->fetchAll();
            
            // Total discussions by status
            $sql = "SELECT status, COUNT(*) as total FROM forum_diskusi";
            $params = [];
            
            if (!empty($filters['user_id'])) {
                $sql .= " WHERE created_by = ?";
                $params[] = $filters['user_id'];
            }
            
            $sql .= " GROUP BY status";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['by_status'] = $stmt->fetchAll();
            
            // Recent activity
            $sql = "SELECT COUNT(*) as total FROM forum_diskusi 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $params = [];
            
            if (!empty($filters['user_id'])) {
                $sql .= " AND created_by = ?";
                $params[] = $filters['user_id'];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['recent_discussions'] = $stmt->fetchColumn();
            
            return $stats;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching discussion statistics", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Check if user can edit discussion
     *
     * @param int $discussionId Discussion ID
     * @param int $userId User ID
     * @return bool True if user can edit
     */
    private function canEditDiskusi(int $discussionId, int $userId): bool
    {
        try {
            // Check if user is the creator
            $sql = "SELECT created_by FROM forum_diskusi WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$discussionId]);
            $discussion = $stmt->fetch();
            
            if ($discussion && $discussion['created_by'] == $userId) {
                return true;
            }
            
            // Check if user is admin
            return $this->isAdmin($userId);
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error checking edit permission", ['discussion_id' => $discussionId, 'user_id' => $userId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Check if user is admin
     *
     * @param int $userId User ID
     * @return bool True if user is admin
     */
    private function isAdmin(int $userId): bool
    {
        try {
            $sql = "SELECT r.nama FROM users u JOIN roles r ON u.role_id = r.id WHERE u.id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $role = $stmt->fetchColumn();
            
            return $role === 'admin';
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error checking admin status", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Search discussions
     *
     * @param string $query Search query
     * @param array $filters Additional filters
     * @return array Search results
     */
    public function searchDiskusi(string $query, array $filters = []): array
    {
        try {
            $sql = "SELECT fd.*, u.username as created_by_username,
                           COUNT(pf.id) as total_replies
                    FROM forum_diskusi fd
                    JOIN users u ON fd.created_by = u.id
                    LEFT JOIN pesan_forum pf ON fd.id = pf.forum_diskusi_id
                    WHERE (fd.judul LIKE ? OR fd.deskripsi LIKE ?)";
            
            $searchTerm = '%' . $query . '%';
            $params = [$searchTerm, $searchTerm];
            
            if (!empty($filters['kategori'])) {
                $sql .= " AND fd.kategori = ?";
                $params[] = $filters['kategori'];
            }
            
            if (!empty($filters['status'])) {
                $sql .= " AND fd.status = ?";
                $params[] = $filters['status'];
            }
            
            $sql .= " GROUP BY fd.id ORDER BY fd.created_at DESC LIMIT 50";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error searching discussions", ['query' => $query, 'error' => $e->getMessage()]);
            return [];
        }
    }
}
