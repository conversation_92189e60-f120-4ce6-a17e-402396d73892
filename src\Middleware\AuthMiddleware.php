<?php

declare(strict_types=1);

namespace Middleware;

use Contracts\Logger\LoggerInterface;
use Providers\Logger\LoggerProvider;

/**
 * Authentication Middleware
 *
 * Handles authentication checks for protected routes
 */
class AuthMiddleware
{
    /**
     * Routes that don't require authentication
     */
    private array $publicRoutes = [
        '',
        '/',
        '/login',
        '/auth/login',
        '/auth/authenticate',
        '/register',
        '/auth/register'
    ];

    /**
     * @var array Role-based route access control
     */
    private array $roleRoutes = [
        'admin' => ['/admin'],
        'guru' => ['/guru', '/teacher'],
        'siswa' => ['/siswa', '/student'],
        'orang_tua' => ['/orang-tua', '/parent']
    ];

    /**
     * Logger instance
     */
    private LoggerInterface $logger;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logger = LoggerProvider::getLogger(LoggerProvider::CATEGORY_AUTH);
    }

    /**
     * Handle the authentication check
     */
    public function handle(): void
    {
        $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $basePath = parse_url(APP_URL, PHP_URL_PATH) ?? '';
        $relativePath = substr($currentPath, strlen($basePath));

        // If on a public route, no auth check needed
        if (in_array($relativePath, $this->publicRoutes)) {
            $this->logger->debug("Public route accessed: {$relativePath}");
            return;
        }

        // If not logged in, redirect to login page
        if (!isset($_SESSION['user_id'])) {
            $this->logger->info("Unauthenticated access attempt: {$relativePath}");
            header('Location: ' . APP_URL . '/login');
            exit;
        }

        // Check role-based access
        $userRole = $_SESSION['role_name'] ?? null;
        if ($userRole && !$this->hasRoleAccess($userRole, $relativePath)) {
            $this->logger->warning("Unauthorized role access attempt", [
                'user_id' => $_SESSION['user_id'],
                'role' => $userRole,
                'path' => $relativePath
            ]);

            // Redirect to appropriate dashboard
            $dashboardUrl = $this->getDashboardUrlForRole($userRole);
            header('Location: ' . APP_URL . $dashboardUrl);
            exit;
        }

        $this->logger->debug("Authenticated access: {$relativePath}", [
            'user_id' => $_SESSION['user_id'],
            'role' => $userRole
        ]);
    }

    /**
     * Check if user role has access to the given path
     *
     * @param string $role User role
     * @param string $path Request path
     * @return bool True if access is allowed
     */
    private function hasRoleAccess(string $role, string $path): bool
    {
        // Admin has access to everything
        if ($role === 'admin') {
            return true;
        }

        // Check if path starts with any allowed role prefix
        if (isset($this->roleRoutes[$role])) {
            foreach ($this->roleRoutes[$role] as $allowedPrefix) {
                if (str_starts_with($path, $allowedPrefix)) {
                    return true;
                }
            }
        }

        // Allow access to general dashboard and profile routes
        $generalRoutes = ['/dashboard', '/profile', '/logout', '/auth/logout'];
        return in_array($path, $generalRoutes);
    }

    /**
     * Get dashboard URL for role
     *
     * @param string $role User role
     * @return string Dashboard URL
     */
    private function getDashboardUrlForRole(string $role): string
    {
        return match ($role) {
            'admin' => '/admin/dashboard',
            'guru' => '/guru/dashboard',
            'siswa' => '/siswa/dashboard',
            'orang_tua' => '/orang-tua/dashboard',
            default => '/dashboard'
        };
    }
}
