<?php

declare(strict_types=1);

namespace Modules\Siswa\Models;

use Core\Model;
use PDO;

/**
 * Siswa Model
 *
 * Handles data operations for student management
 *
 * @package Modules\Siswa\Models
 */
class SiswaModel extends Model
{
    /**
     * Get student profile by user ID
     *
     * @param int $userId User ID
     * @return array|null Student profile data
     */
    public function getProfileByUserId(int $userId): ?array
    {
        try {
            $sql = "SELECT s.*, ot.nama_lengkap as nama_orang_tua, ot.no_telepon as telepon_orang_tua,
                           u.username, u.email, u_ot.email as email_orang_tua
                    FROM siswa s
                    JOIN users u ON s.user_id = u.id
                    JOIN orang_tua ot ON s.orang_tua_id = ot.id
                    JOIN users u_ot ON ot.user_id = u_ot.id
                    WHERE s.user_id = ? AND s.status_aktif = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching student profile", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get student classes
     *
     * @param int $siswaId Student ID
     * @return array List of classes
     */
    public function getKelas(int $siswaId): array
    {
        try {
            $sql = "SELECT kb.*, mp.nama as mata_pelajaran, g.nama_lengkap as nama_guru,
                           pk.status as status_pendaftaran, pk.tanggal_daftar
                    FROM pendaftaran_kelas pk
                    JOIN kelas_bimbel kb ON pk.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    JOIN guru g ON kb.guru_id = g.id
                    WHERE pk.siswa_id = ? AND pk.status = 'aktif'
                    ORDER BY kb.jadwal_hari, kb.jam_mulai";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching student classes", ['siswa_id' => $siswaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get student grades
     *
     * @param int $siswaId Student ID
     * @param int|null $kelasId Optional class ID filter
     * @return array List of grades
     */
    public function getNilai(int $siswaId, ?int $kelasId = null): array
    {
        try {
            $sql = "SELECT n.*, kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           rp.topik, rp.tanggal_rencana
                    FROM nilai n
                    JOIN kelas_bimbel kb ON n.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    LEFT JOIN rencana_pengajaran rp ON n.rencana_pengajaran_id = rp.id
                    WHERE n.siswa_id = ?";
            
            $params = [$siswaId];
            
            if ($kelasId) {
                $sql .= " AND n.kelas_bimbel_id = ?";
                $params[] = $kelasId;
            }
            
            $sql .= " ORDER BY n.tanggal_penilaian DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching student grades", ['siswa_id' => $siswaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get student attendance
     *
     * @param int $siswaId Student ID
     * @param int|null $kelasId Optional class ID filter
     * @param string|null $bulan Optional month filter (YYYY-MM)
     * @return array List of attendance records
     */
    public function getPresensi(int $siswaId, ?int $kelasId = null, ?string $bulan = null): array
    {
        try {
            $sql = "SELECT p.*, kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           rp.topik, rp.pertemuan_ke
                    FROM presensi p
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    LEFT JOIN rencana_pengajaran rp ON p.rencana_pengajaran_id = rp.id
                    WHERE p.siswa_id = ?";
            
            $params = [$siswaId];
            
            if ($kelasId) {
                $sql .= " AND p.kelas_bimbel_id = ?";
                $params[] = $kelasId;
            }
            
            if ($bulan) {
                $sql .= " AND DATE_FORMAT(p.tanggal, '%Y-%m') = ?";
                $params[] = $bulan;
            }
            
            $sql .= " ORDER BY p.tanggal DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching student attendance", ['siswa_id' => $siswaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get student payment history
     *
     * @param int $siswaId Student ID
     * @return array List of payment records
     */
    public function getPembayaran(int $siswaId): array
    {
        try {
            $sql = "SELECT p.*, kb.nama as nama_kelas, mp.nama as mata_pelajaran
                    FROM pembayaran p
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    WHERE p.siswa_id = ?
                    ORDER BY p.bulan_tahun DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching student payments", ['siswa_id' => $siswaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Update student profile
     *
     * @param int $siswaId Student ID
     * @param array $data Profile data
     * @return bool Success status
     */
    public function updateProfile(int $siswaId, array $data): bool
    {
        try {
            $sql = "UPDATE siswa SET 
                        nama_lengkap = ?, 
                        tempat_lahir = ?, 
                        tanggal_lahir = ?, 
                        alamat = ?, 
                        no_telepon = ?, 
                        sekolah_asal = ?, 
                        kelas_sekolah = ?,
                        updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['nama_lengkap'],
                $data['tempat_lahir'],
                $data['tanggal_lahir'],
                $data['alamat'],
                $data['no_telepon'],
                $data['sekolah_asal'],
                $data['kelas_sekolah'],
                $siswaId
            ]);

            if ($result) {
                $this->getLogger()->info("Student profile updated", ['siswa_id' => $siswaId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error updating student profile", ['siswa_id' => $siswaId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get student statistics
     *
     * @param int $siswaId Student ID
     * @return array Statistics data
     */
    public function getStatistik(int $siswaId): array
    {
        try {
            $stats = [];
            
            // Total kelas aktif
            $sql = "SELECT COUNT(*) as total FROM pendaftaran_kelas WHERE siswa_id = ? AND status = 'aktif'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            $stats['total_kelas'] = $stmt->fetchColumn();
            
            // Rata-rata nilai
            $sql = "SELECT AVG(nilai) as rata_rata FROM nilai WHERE siswa_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            $stats['rata_rata_nilai'] = round($stmt->fetchColumn(), 2);
            
            // Persentase kehadiran bulan ini
            $sql = "SELECT 
                        COUNT(*) as total_pertemuan,
                        SUM(CASE WHEN status = 'hadir' THEN 1 ELSE 0 END) as total_hadir
                    FROM presensi 
                    WHERE siswa_id = ? AND MONTH(tanggal) = MONTH(NOW()) AND YEAR(tanggal) = YEAR(NOW())";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            $presensi = $stmt->fetch();
            
            if ($presensi['total_pertemuan'] > 0) {
                $stats['persentase_kehadiran'] = round(($presensi['total_hadir'] / $presensi['total_pertemuan']) * 100, 2);
            } else {
                $stats['persentase_kehadiran'] = 0;
            }
            
            // Pembayaran tertunggak
            $sql = "SELECT COUNT(*) as total FROM pembayaran WHERE siswa_id = ? AND status IN ('belum_bayar', 'terlambat')";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            $stats['pembayaran_tertunggak'] = $stmt->fetchColumn();
            
            return $stats;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching student statistics", ['siswa_id' => $siswaId, 'error' => $e->getMessage()]);
            return [];
        }
    }
}
