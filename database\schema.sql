-- =====================================================
-- SCHEMA DATABASE SISTEM BIMBEL ONLINE
-- =====================================================
-- Dibuat untuk aplikasi bimbel online dengan fitur:
-- 1. Pelaporan ke Orang Tua
-- 2. Pengingat Pembayaran
-- 3. Rencana Pengajaran
-- 4. Template Dokumen
-- 5. A<PERSON><PERSON> dan <PERSON>
-- =====================================================

-- Tabel Roles untuk sistem multi-role
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama VARCHAR(50) NOT NULL UNIQUE,
    deskripsi TEXT,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Users (pengguna umum)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role_id INT NOT NULL,
    status_aktif BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE RESTRICT
);

-- Tabel Mata Pelajaran
CREATE TABLE mata_pelajaran (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama VARCHAR(100) NOT NULL,
    kode VARCHAR(20) NOT NULL UNIQUE,
    deskripsi TEXT,
    tingkat_kelas VARCHAR(50),
    status_aktif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Guru
CREATE TABLE guru (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    nip VARCHAR(50) UNIQUE,
    nama_lengkap VARCHAR(255) NOT NULL,
    tempat_lahir VARCHAR(100),
    tanggal_lahir DATE,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    alamat TEXT,
    no_telepon VARCHAR(20),
    pendidikan_terakhir VARCHAR(100),
    spesialisasi TEXT,
    foto VARCHAR(255),
    status_aktif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabel Guru Mata Pelajaran (relasi many-to-many)
CREATE TABLE guru_mata_pelajaran (
    id INT PRIMARY KEY AUTO_INCREMENT,
    guru_id INT NOT NULL,
    mata_pelajaran_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (guru_id) REFERENCES guru(id) ON DELETE CASCADE,
    FOREIGN KEY (mata_pelajaran_id) REFERENCES mata_pelajaran(id) ON DELETE CASCADE,
    UNIQUE KEY unique_guru_mapel (guru_id, mata_pelajaran_id)
);

-- Tabel Orang Tua
CREATE TABLE orang_tua (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    nama_lengkap VARCHAR(255) NOT NULL,
    tempat_lahir VARCHAR(100),
    tanggal_lahir DATE,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    alamat TEXT,
    no_telepon VARCHAR(20),
    pekerjaan VARCHAR(100),
    pendidikan VARCHAR(100),
    foto VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabel Siswa
CREATE TABLE siswa (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    nis VARCHAR(50) UNIQUE,
    nama_lengkap VARCHAR(255) NOT NULL,
    tempat_lahir VARCHAR(100),
    tanggal_lahir DATE,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    alamat TEXT,
    no_telepon VARCHAR(20),
    sekolah_asal VARCHAR(255),
    kelas_sekolah VARCHAR(50),
    foto VARCHAR(255),
    orang_tua_id INT NOT NULL,
    status_aktif BOOLEAN DEFAULT TRUE,
    tanggal_daftar DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (orang_tua_id) REFERENCES orang_tua(id) ON DELETE RESTRICT
);

-- Tabel Kelas Bimbel
CREATE TABLE kelas_bimbel (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama VARCHAR(100) NOT NULL,
    kode VARCHAR(20) NOT NULL UNIQUE,
    mata_pelajaran_id INT NOT NULL,
    guru_id INT NOT NULL,
    tingkat VARCHAR(50) NOT NULL,
    kapasitas_maksimal INT DEFAULT 20,
    biaya_per_bulan DECIMAL(10,2) NOT NULL,
    jadwal_hari ENUM('Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu') NOT NULL,
    jam_mulai TIME NOT NULL,
    jam_selesai TIME NOT NULL,
    ruangan VARCHAR(50),
    deskripsi TEXT,
    status_aktif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (mata_pelajaran_id) REFERENCES mata_pelajaran(id) ON DELETE RESTRICT,
    FOREIGN KEY (guru_id) REFERENCES guru(id) ON DELETE RESTRICT
);

-- Tabel Pendaftaran Siswa ke Kelas
CREATE TABLE pendaftaran_kelas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    kelas_bimbel_id INT NOT NULL,
    tanggal_daftar DATE DEFAULT (CURRENT_DATE),
    status ENUM('aktif', 'nonaktif', 'lulus', 'pindah') DEFAULT 'aktif',
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_bimbel_id) REFERENCES kelas_bimbel(id) ON DELETE CASCADE,
    UNIQUE KEY unique_siswa_kelas (siswa_id, kelas_bimbel_id)
);

-- Tabel Rencana Pengajaran
CREATE TABLE rencana_pengajaran (
    id INT PRIMARY KEY AUTO_INCREMENT,
    kelas_bimbel_id INT NOT NULL,
    guru_id INT NOT NULL,
    pertemuan_ke INT NOT NULL,
    tanggal_rencana DATE NOT NULL,
    topik VARCHAR(255) NOT NULL,
    tujuan_pembelajaran TEXT NOT NULL,
    materi_pokok TEXT NOT NULL,
    metode_pembelajaran TEXT,
    media_pembelajaran TEXT,
    evaluasi TEXT,
    tugas_rumah TEXT,
    catatan_guru TEXT,
    status ENUM('draft', 'approved', 'completed') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (kelas_bimbel_id) REFERENCES kelas_bimbel(id) ON DELETE CASCADE,
    FOREIGN KEY (guru_id) REFERENCES guru(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_kelas_pertemuan (kelas_bimbel_id, pertemuan_ke)
);

-- Tabel Presensi
CREATE TABLE presensi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    kelas_bimbel_id INT NOT NULL,
    rencana_pengajaran_id INT,
    tanggal DATE NOT NULL,
    jam_masuk TIME,
    jam_keluar TIME,
    status ENUM('hadir', 'tidak_hadir', 'izin', 'sakit', 'terlambat') NOT NULL,
    catatan TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_bimbel_id) REFERENCES kelas_bimbel(id) ON DELETE CASCADE,
    FOREIGN KEY (rencana_pengajaran_id) REFERENCES rencana_pengajaran(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_siswa_tanggal_kelas (siswa_id, tanggal, kelas_bimbel_id)
);

-- Tabel Nilai
CREATE TABLE nilai (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    kelas_bimbel_id INT NOT NULL,
    rencana_pengajaran_id INT,
    jenis_nilai ENUM('tugas', 'kuis', 'ujian', 'praktik', 'project') NOT NULL,
    nama_penilaian VARCHAR(255) NOT NULL,
    nilai DECIMAL(5,2) NOT NULL,
    nilai_maksimal DECIMAL(5,2) DEFAULT 100.00,
    tanggal_penilaian DATE NOT NULL,
    catatan TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_bimbel_id) REFERENCES kelas_bimbel(id) ON DELETE CASCADE,
    FOREIGN KEY (rencana_pengajaran_id) REFERENCES rencana_pengajaran(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
);

-- Tabel Pembayaran
CREATE TABLE pembayaran (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    kelas_bimbel_id INT NOT NULL,
    bulan_tahun VARCHAR(7) NOT NULL, -- Format: YYYY-MM
    jumlah_tagihan DECIMAL(10,2) NOT NULL,
    jumlah_dibayar DECIMAL(10,2) DEFAULT 0.00,
    tanggal_jatuh_tempo DATE NOT NULL,
    tanggal_pembayaran DATETIME NULL,
    status ENUM('belum_bayar', 'sebagian', 'lunas', 'terlambat') DEFAULT 'belum_bayar',
    metode_pembayaran ENUM('tunai', 'transfer', 'kartu_kredit', 'e_wallet') NULL,
    bukti_pembayaran VARCHAR(255),
    catatan TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_bimbel_id) REFERENCES kelas_bimbel(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_siswa_bulan_kelas (siswa_id, kelas_bimbel_id, bulan_tahun)
);

-- Tabel Pengingat Pembayaran
CREATE TABLE pengingat_pembayaran (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pembayaran_id INT NOT NULL,
    jenis_pengingat ENUM('email', 'sms', 'whatsapp', 'notifikasi') NOT NULL,
    tanggal_kirim DATETIME NOT NULL,
    status ENUM('pending', 'terkirim', 'gagal') DEFAULT 'pending',
    pesan TEXT,
    response_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pembayaran_id) REFERENCES pembayaran(id) ON DELETE CASCADE
);

-- Tabel Laporan Perkembangan
CREATE TABLE laporan_perkembangan (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    kelas_bimbel_id INT NOT NULL,
    periode VARCHAR(20) NOT NULL, -- Format: semester-tahun (misal: 1-2024)
    rata_rata_nilai DECIMAL(5,2),
    total_kehadiran INT DEFAULT 0,
    total_pertemuan INT DEFAULT 0,
    persentase_kehadiran DECIMAL(5,2),
    catatan_guru TEXT,
    rekomendasi TEXT,
    tanggal_laporan DATE NOT NULL,
    status ENUM('draft', 'final', 'terkirim') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_bimbel_id) REFERENCES kelas_bimbel(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_siswa_periode_kelas (siswa_id, kelas_bimbel_id, periode)
);

-- Tabel Forum Diskusi
CREATE TABLE forum_diskusi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    judul VARCHAR(255) NOT NULL,
    kategori ENUM('operasional', 'pengembangan', 'kendala', 'umum') NOT NULL,
    deskripsi TEXT,
    status ENUM('aktif', 'ditutup', 'arsip') DEFAULT 'aktif',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
);

-- Tabel Pesan Forum
CREATE TABLE pesan_forum (
    id INT PRIMARY KEY AUTO_INCREMENT,
    forum_diskusi_id INT NOT NULL,
    parent_id INT NULL, -- untuk reply
    user_id INT NOT NULL,
    pesan TEXT NOT NULL,
    file_attachment VARCHAR(255),
    is_solution BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (forum_diskusi_id) REFERENCES forum_diskusi(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES pesan_forum(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
);

-- Tabel Template Dokumen
CREATE TABLE template_dokumen (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama VARCHAR(255) NOT NULL,
    jenis ENUM('absensi', 'laporan_nilai', 'data_siswa', 'surat', 'formulir') NOT NULL,
    deskripsi TEXT,
    file_template VARCHAR(255) NOT NULL,
    variables JSON, -- variabel yang bisa digunakan dalam template
    status_aktif BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
);

-- Tabel Dokumen yang Dihasilkan
CREATE TABLE dokumen_generated (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_dokumen_id INT NOT NULL,
    nama_file VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    data_variables JSON, -- data yang digunakan untuk generate dokumen
    generated_by INT NOT NULL,
    generated_for_siswa_id INT NULL,
    generated_for_kelas_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_dokumen_id) REFERENCES template_dokumen(id) ON DELETE RESTRICT,
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (generated_for_siswa_id) REFERENCES siswa(id) ON DELETE SET NULL,
    FOREIGN KEY (generated_for_kelas_id) REFERENCES kelas_bimbel(id) ON DELETE SET NULL
);

-- Tabel Notifikasi
CREATE TABLE notifikasi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    judul VARCHAR(255) NOT NULL,
    pesan TEXT NOT NULL,
    jenis ENUM('info', 'warning', 'success', 'error') DEFAULT 'info',
    kategori ENUM('pembayaran', 'presensi', 'nilai', 'pengumuman', 'sistem') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255),
    data_payload JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabel Pengaturan Sistem
CREATE TABLE pengaturan_sistem (
    id INT PRIMARY KEY AUTO_INCREMENT,
    kunci VARCHAR(100) NOT NULL UNIQUE,
    nilai TEXT NOT NULL,
    deskripsi TEXT,
    kategori VARCHAR(50),
    tipe_data ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- =====================================================
-- DATA AWAL (SEED DATA)
-- =====================================================

-- Insert Roles
INSERT INTO roles (nama, deskripsi, permissions) VALUES
('admin', 'Administrator sistem', '["all"]'),
('guru', 'Guru/Pengajar', '["manage_class", "manage_students", "view_reports", "create_lesson_plan"]'),
('siswa', 'Siswa bimbel', '["view_profile", "view_grades", "view_schedule"]'),
('orang_tua', 'Orang tua siswa', '["view_child_progress", "view_payments", "receive_reports"]');

-- Insert Admin User
INSERT INTO users (username, email, password, role_id) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);

-- Insert Mata Pelajaran
INSERT INTO mata_pelajaran (nama, kode, deskripsi, tingkat_kelas) VALUES
('Matematika', 'MTK', 'Mata pelajaran matematika', 'SD,SMP,SMA'),
('Bahasa Indonesia', 'BID', 'Mata pelajaran bahasa Indonesia', 'SD,SMP,SMA'),
('Bahasa Inggris', 'BIG', 'Mata pelajaran bahasa Inggris', 'SD,SMP,SMA'),
('IPA', 'IPA', 'Ilmu Pengetahuan Alam', 'SD,SMP'),
('Fisika', 'FIS', 'Mata pelajaran fisika', 'SMP,SMA'),
('Kimia', 'KIM', 'Mata pelajaran kimia', 'SMP,SMA'),
('Biologi', 'BIO', 'Mata pelajaran biologi', 'SMP,SMA'),
('IPS', 'IPS', 'Ilmu Pengetahuan Sosial', 'SD,SMP'),
('Sejarah', 'SEJ', 'Mata pelajaran sejarah', 'SMP,SMA'),
('Geografi', 'GEO', 'Mata pelajaran geografi', 'SMP,SMA');

-- Insert Template Dokumen Default
INSERT INTO template_dokumen (nama, jenis, deskripsi, file_template, variables, created_by) VALUES
('Template Absensi Harian', 'absensi', 'Template untuk absensi harian siswa', 'templates/absensi_harian.html',
'{"tanggal": "date", "kelas": "string", "guru": "string", "siswa_list": "array"}', 1),
('Template Laporan Nilai', 'laporan_nilai', 'Template untuk laporan nilai siswa', 'templates/laporan_nilai.html',
'{"siswa": "object", "nilai_list": "array", "periode": "string"}', 1),
('Template Data Siswa', 'data_siswa', 'Template untuk data lengkap siswa', 'templates/data_siswa.html',
'{"siswa": "object", "orang_tua": "object", "kelas_list": "array"}', 1);

-- Insert Pengaturan Sistem Default
INSERT INTO pengaturan_sistem (kunci, nilai, deskripsi, kategori, tipe_data) VALUES
('app_name', 'Bimbel Online', 'Nama aplikasi', 'general', 'string'),
('app_version', '1.0.0', 'Versi aplikasi', 'general', 'string'),
('timezone', 'Asia/Jakarta', 'Timezone aplikasi', 'general', 'string'),
('currency', 'IDR', 'Mata uang yang digunakan', 'payment', 'string'),
('payment_reminder_days', '3', 'Hari sebelum jatuh tempo untuk mengirim pengingat', 'payment', 'number'),
('max_students_per_class', '20', 'Maksimal siswa per kelas', 'class', 'number'),
('semester_duration_months', '6', 'Durasi semester dalam bulan', 'academic', 'number'),
('auto_generate_reports', 'true', 'Otomatis generate laporan bulanan', 'reports', 'boolean'),
('email_notifications', 'true', 'Aktifkan notifikasi email', 'notifications', 'boolean'),
('sms_notifications', 'false', 'Aktifkan notifikasi SMS', 'notifications', 'boolean');

-- =====================================================
-- INDEXES UNTUK PERFORMA
-- =====================================================

-- Index untuk pencarian dan performa
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role_id);
CREATE INDEX idx_siswa_nama ON siswa(nama_lengkap);
CREATE INDEX idx_guru_nama ON guru(nama_lengkap);
CREATE INDEX idx_presensi_tanggal ON presensi(tanggal);
CREATE INDEX idx_presensi_siswa_tanggal ON presensi(siswa_id, tanggal);
CREATE INDEX idx_nilai_siswa ON nilai(siswa_id);
CREATE INDEX idx_pembayaran_status ON pembayaran(status);
CREATE INDEX idx_pembayaran_jatuh_tempo ON pembayaran(tanggal_jatuh_tempo);
CREATE INDEX idx_notifikasi_user_read ON notifikasi(user_id, is_read);
CREATE INDEX idx_forum_kategori ON forum_diskusi(kategori);
CREATE INDEX idx_rencana_pengajaran_kelas ON rencana_pengajaran(kelas_bimbel_id);

-- =====================================================
-- VIEWS UNTUK KEMUDAHAN QUERY
-- =====================================================

-- View untuk informasi lengkap siswa
CREATE VIEW v_siswa_lengkap AS
SELECT
    s.id,
    s.nis,
    s.nama_lengkap,
    s.tempat_lahir,
    s.tanggal_lahir,
    s.jenis_kelamin,
    s.alamat,
    s.no_telepon,
    s.sekolah_asal,
    s.kelas_sekolah,
    s.status_aktif,
    s.tanggal_daftar,
    u.username,
    u.email,
    ot.nama_lengkap as nama_orang_tua,
    ot.no_telepon as telepon_orang_tua,
    u_ot.email as email_orang_tua
FROM siswa s
JOIN users u ON s.user_id = u.id
JOIN orang_tua ot ON s.orang_tua_id = ot.id
JOIN users u_ot ON ot.user_id = u_ot.id;

-- View untuk informasi kelas dengan guru dan mata pelajaran
CREATE VIEW v_kelas_lengkap AS
SELECT
    kb.id,
    kb.nama as nama_kelas,
    kb.kode,
    kb.tingkat,
    kb.kapasitas_maksimal,
    kb.biaya_per_bulan,
    kb.jadwal_hari,
    kb.jam_mulai,
    kb.jam_selesai,
    kb.ruangan,
    kb.status_aktif,
    mp.nama as mata_pelajaran,
    mp.kode as kode_mapel,
    g.nama_lengkap as nama_guru,
    g.no_telepon as telepon_guru,
    COUNT(pk.siswa_id) as jumlah_siswa
FROM kelas_bimbel kb
JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
JOIN guru g ON kb.guru_id = g.id
LEFT JOIN pendaftaran_kelas pk ON kb.id = pk.kelas_bimbel_id AND pk.status = 'aktif'
GROUP BY kb.id, mp.id, g.id;