<?php

declare(strict_types=1);

/**
 * Pembayaran Module Routes Configuration
 *
 * Defines routes for the Pembayaran module.
 * Follows PSR standards and HMVC architecture patterns.
 *
 * @package Modules\Pembayaran\Config
 */

return [
    'name' => 'Pembayaran',
    'namespace' => 'Modules\\Pembayaran',
    'default_controller' => 'PembayaranController',
    'default_action' => 'index',

    'routes' => [
        'pembayaran' => [
            'controller' => 'PembayaranController',
            'action' => 'index'
        ],
        'pembayaran/create' => [
            'controller' => 'PembayaranController',
            'action' => 'create'
        ],
        'pembayaran/{id}' => [
            'controller' => 'PembayaranController',
            'action' => 'detail'
        ],
        'pembayaran/{id}/edit' => [
            'controller' => 'PembayaranController',
            'action' => 'edit'
        ],
        'pembayaran/{id}/update' => [
            'controller' => 'PembayaranController',
            'action' => 'update'
        ],
        'pembayaran/{id}/bayar' => [
            'controller' => 'PembayaranController',
            'action' => 'bayar'
        ],
        'pembayaran/{id}/konfirmasi' => [
            'controller' => 'PembayaranController',
            'action' => 'konfirmasi'
        ],
        'pembayaran/generate-tagihan' => [
            'controller' => 'TagihanController',
            'action' => 'generate'
        ],
        'pembayaran/tagihan' => [
            'controller' => 'TagihanController',
            'action' => 'index'
        ],
        'pembayaran/tagihan/{id}' => [
            'controller' => 'TagihanController',
            'action' => 'detail'
        ],
        'pembayaran/reminder' => [
            'controller' => 'ReminderController',
            'action' => 'index'
        ],
        'pembayaran/reminder/send' => [
            'controller' => 'ReminderController',
            'action' => 'send'
        ],
        'pembayaran/reminder/auto-send' => [
            'controller' => 'ReminderController',
            'action' => 'autoSend'
        ],
        'pembayaran/reminder/settings' => [
            'controller' => 'ReminderController',
            'action' => 'settings'
        ],
        'pembayaran/laporan' => [
            'controller' => 'LaporanController',
            'action' => 'index'
        ],
        'pembayaran/laporan/generate' => [
            'controller' => 'LaporanController',
            'action' => 'generate'
        ],
        'pembayaran/laporan/download' => [
            'controller' => 'LaporanController',
            'action' => 'download'
        ]
    ]
];
