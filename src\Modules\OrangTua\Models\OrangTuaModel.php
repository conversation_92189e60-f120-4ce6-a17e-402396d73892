<?php

declare(strict_types=1);

namespace Modules\OrangTua\Models;

use Core\Model;
use PDO;

/**
 * Orang Tua Model
 *
 * Handles data operations for parent management
 *
 * @package Modules\OrangTua\Models
 */
class OrangTuaModel extends Model
{
    /**
     * Get parent profile by user ID
     *
     * @param int $userId User ID
     * @return array|null Parent profile data
     */
    public function getProfileByUserId(int $userId): ?array
    {
        try {
            $sql = "SELECT ot.*, u.username, u.email
                    FROM orang_tua ot
                    JOIN users u ON ot.user_id = u.id
                    WHERE ot.user_id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching parent profile", ['user_id' => $userId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get children list for parent
     *
     * @param int $orangTuaId Parent ID
     * @return array List of children
     */
    public function getDaftarAnak(int $orangTuaId): array
    {
        try {
            $sql = "SELECT s.*, u.username, u.email,
                           COUNT(pk.id) as total_kelas_aktif
                    FROM siswa s
                    JOIN users u ON s.user_id = u.id
                    LEFT JOIN pendaftaran_kelas pk ON s.id = pk.siswa_id AND pk.status = 'aktif'
                    WHERE s.orang_tua_id = ? AND s.status_aktif = 1
                    GROUP BY s.id
                    ORDER BY s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$orangTuaId]);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching children list", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get detailed information about a child
     *
     * @param int $siswaId Student ID
     * @param int $orangTuaId Parent ID (for security check)
     * @return array|null Child details
     */
    public function getDetailAnak(int $siswaId, int $orangTuaId): ?array
    {
        try {
            $sql = "SELECT s.*, u.username, u.email
                    FROM siswa s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.id = ? AND s.orang_tua_id = ? AND s.status_aktif = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId, $orangTuaId]);
            $siswa = $stmt->fetch();
            
            if (!$siswa) {
                return null;
            }
            
            // Get classes
            $sql = "SELECT kb.*, mp.nama as mata_pelajaran, g.nama_lengkap as nama_guru,
                           pk.status as status_pendaftaran, pk.tanggal_daftar
                    FROM pendaftaran_kelas pk
                    JOIN kelas_bimbel kb ON pk.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    JOIN guru g ON kb.guru_id = g.id
                    WHERE pk.siswa_id = ? AND pk.status = 'aktif'
                    ORDER BY kb.jadwal_hari, kb.jam_mulai";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$siswaId]);
            $siswa['kelas'] = $stmt->fetchAll();
            
            return $siswa;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching child details", ['siswa_id' => $siswaId, 'orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get progress reports for children
     *
     * @param int $orangTuaId Parent ID
     * @param int|null $siswaId Optional student ID filter
     * @return array List of progress reports
     */
    public function getLaporanPerkembangan(int $orangTuaId, ?int $siswaId = null): array
    {
        try {
            $sql = "SELECT lp.*, s.nama_lengkap as nama_siswa, kb.nama as nama_kelas, 
                           mp.nama as mata_pelajaran, g.nama_lengkap as nama_guru
                    FROM laporan_perkembangan lp
                    JOIN siswa s ON lp.siswa_id = s.id
                    JOIN kelas_bimbel kb ON lp.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    JOIN guru g ON kb.guru_id = g.id
                    WHERE s.orang_tua_id = ? AND lp.status IN ('final', 'terkirim')";
            
            $params = [$orangTuaId];
            
            if ($siswaId) {
                $sql .= " AND lp.siswa_id = ?";
                $params[] = $siswaId;
            }
            
            $sql .= " ORDER BY lp.periode DESC, s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching progress reports", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get payment information for children
     *
     * @param int $orangTuaId Parent ID
     * @param string|null $status Payment status filter
     * @return array List of payments
     */
    public function getPembayaran(int $orangTuaId, ?string $status = null): array
    {
        try {
            $sql = "SELECT p.*, s.nama_lengkap as nama_siswa, kb.nama as nama_kelas, 
                           mp.nama as mata_pelajaran
                    FROM pembayaran p
                    JOIN siswa s ON p.siswa_id = s.id
                    JOIN kelas_bimbel kb ON p.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    WHERE s.orang_tua_id = ?";
            
            $params = [$orangTuaId];
            
            if ($status) {
                $sql .= " AND p.status = ?";
                $params[] = $status;
            }
            
            $sql .= " ORDER BY p.tanggal_jatuh_tempo DESC, s.nama_lengkap";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching payments", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get attendance summary for children
     *
     * @param int $orangTuaId Parent ID
     * @param string|null $bulan Month filter (YYYY-MM)
     * @return array Attendance summary
     */
    public function getRekapPresensi(int $orangTuaId, ?string $bulan = null): array
    {
        try {
            $sql = "SELECT s.id as siswa_id, s.nama_lengkap as nama_siswa,
                           kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           COUNT(p.id) as total_pertemuan,
                           SUM(CASE WHEN p.status = 'hadir' THEN 1 ELSE 0 END) as total_hadir,
                           SUM(CASE WHEN p.status = 'tidak_hadir' THEN 1 ELSE 0 END) as total_tidak_hadir,
                           SUM(CASE WHEN p.status = 'izin' THEN 1 ELSE 0 END) as total_izin,
                           SUM(CASE WHEN p.status = 'sakit' THEN 1 ELSE 0 END) as total_sakit
                    FROM siswa s
                    JOIN pendaftaran_kelas pk ON s.id = pk.siswa_id AND pk.status = 'aktif'
                    JOIN kelas_bimbel kb ON pk.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    LEFT JOIN presensi p ON s.id = p.siswa_id AND p.kelas_bimbel_id = kb.id";
            
            $params = [$orangTuaId];
            
            if ($bulan) {
                $sql .= " AND DATE_FORMAT(p.tanggal, '%Y-%m') = ?";
                $params[] = $bulan;
            }
            
            $sql .= " WHERE s.orang_tua_id = ? AND s.status_aktif = 1
                      GROUP BY s.id, kb.id
                      ORDER BY s.nama_lengkap, kb.nama";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching attendance summary", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get grade summary for children
     *
     * @param int $orangTuaId Parent ID
     * @param int|null $siswaId Optional student ID filter
     * @return array Grade summary
     */
    public function getRekapNilai(int $orangTuaId, ?int $siswaId = null): array
    {
        try {
            $sql = "SELECT s.id as siswa_id, s.nama_lengkap as nama_siswa,
                           kb.nama as nama_kelas, mp.nama as mata_pelajaran,
                           n.jenis_nilai, n.nama_penilaian, n.nilai, n.nilai_maksimal,
                           n.tanggal_penilaian, n.catatan
                    FROM siswa s
                    JOIN nilai n ON s.id = n.siswa_id
                    JOIN kelas_bimbel kb ON n.kelas_bimbel_id = kb.id
                    JOIN mata_pelajaran mp ON kb.mata_pelajaran_id = mp.id
                    WHERE s.orang_tua_id = ? AND s.status_aktif = 1";
            
            $params = [$orangTuaId];
            
            if ($siswaId) {
                $sql .= " AND s.id = ?";
                $params[] = $siswaId;
            }
            
            $sql .= " ORDER BY s.nama_lengkap, kb.nama, n.tanggal_penilaian DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching grade summary", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Update parent profile
     *
     * @param int $orangTuaId Parent ID
     * @param array $data Profile data
     * @return bool Success status
     */
    public function updateProfile(int $orangTuaId, array $data): bool
    {
        try {
            $sql = "UPDATE orang_tua SET 
                        nama_lengkap = ?, 
                        tempat_lahir = ?, 
                        tanggal_lahir = ?, 
                        alamat = ?, 
                        no_telepon = ?, 
                        pekerjaan = ?, 
                        pendidikan = ?,
                        updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $data['nama_lengkap'],
                $data['tempat_lahir'],
                $data['tanggal_lahir'],
                $data['alamat'],
                $data['no_telepon'],
                $data['pekerjaan'],
                $data['pendidikan'],
                $orangTuaId
            ]);

            if ($result) {
                $this->getLogger()->info("Parent profile updated", ['orang_tua_id' => $orangTuaId]);
            }

            return $result;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error updating parent profile", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get dashboard statistics for parent
     *
     * @param int $orangTuaId Parent ID
     * @return array Statistics data
     */
    public function getDashboardStats(int $orangTuaId): array
    {
        try {
            $stats = [];
            
            // Total anak
            $sql = "SELECT COUNT(*) as total FROM siswa WHERE orang_tua_id = ? AND status_aktif = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$orangTuaId]);
            $stats['total_anak'] = $stmt->fetchColumn();
            
            // Total kelas aktif
            $sql = "SELECT COUNT(*) as total 
                    FROM pendaftaran_kelas pk
                    JOIN siswa s ON pk.siswa_id = s.id
                    WHERE s.orang_tua_id = ? AND pk.status = 'aktif'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$orangTuaId]);
            $stats['total_kelas_aktif'] = $stmt->fetchColumn();
            
            // Pembayaran tertunggak
            $sql = "SELECT COUNT(*) as total 
                    FROM pembayaran p
                    JOIN siswa s ON p.siswa_id = s.id
                    WHERE s.orang_tua_id = ? AND p.status IN ('belum_bayar', 'terlambat')";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$orangTuaId]);
            $stats['pembayaran_tertunggak'] = $stmt->fetchColumn();
            
            // Laporan terbaru
            $sql = "SELECT COUNT(*) as total 
                    FROM laporan_perkembangan lp
                    JOIN siswa s ON lp.siswa_id = s.id
                    WHERE s.orang_tua_id = ? AND lp.status = 'terkirim' 
                    AND lp.tanggal_laporan >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$orangTuaId]);
            $stats['laporan_baru'] = $stmt->fetchColumn();
            
            return $stats;
        } catch (\PDOException $e) {
            $this->getLogger()->error("Error fetching dashboard statistics", ['orang_tua_id' => $orangTuaId, 'error' => $e->getMessage()]);
            return [];
        }
    }
}
